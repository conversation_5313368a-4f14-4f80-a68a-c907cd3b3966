/// <reference types="webxr" />
import { WebRtcPlayerController } from '../WebRtcPlayer/WebRtcPlayerController';
export declare class WebXRController {
    private xrSession;
    private xrRefSpace;
    private gl;
    private xrViewerPose;
    private positionLocation;
    private texcoordLocation;
    private positionBuffer;
    private texcoordBuffer;
    private videoTexture;
    private prevVideoWidth;
    private prevVideoHeight;
    private webRtcController;
    private xrGamepadController;
    private leftView;
    private rightView;
    onSessionStarted: EventTarget;
    onSessionEnded: EventTarget;
    onFrame: EventTarget;
    constructor(webRtcPlayerController: WebRtcPlayerController);
    xrClicked(): void;
    onXrSessionEnded(): void;
    initGL(): void;
    initShaders(): void;
    updateVideoTexture(): void;
    initBuffers(): void;
    onXrSessionStarted(session: XRSession): void;
    sendXRDataToUE(): void;
    onXrFrame(time: DOMHighResTimeStamp, frame: XRFrame): void;
    private updateViews;
    private render;
    static isSessionSupported(mode: XRSessionMode): Promise<boolean>;
}
