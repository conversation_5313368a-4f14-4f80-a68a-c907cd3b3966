01:50:20.252 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
01:50:20.291 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
01:50:20.293 Redirecting http->https
01:50:20.298 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
01:50:20.305 WebSocket listening for Streamer connections on :8888
01:50:20.305 WebSocket listening for SFU connections on :8889
01:50:20.306 WebSocket listening for Players connections on :80
01:50:20.307 Http listening on *: 80
01:50:20.307 Https listening on *: 443
01:50:44.391 Streamer connected: ::1
01:50:44.392 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:50:44.393 [37m::1 <-[32m {"type":"identify"}
01:50:44.852 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
01:50:44.852 Registered new streamer: Streamer Component
01:51:45.008 [37mStreamer Component ->[34m {"type":"ping","time":1753379504}
01:52:45.017 [37mStreamer Component ->[34m {"type":"ping","time":1753379565}
01:53:44.985 [37mStreamer Component ->[34m {"type":"ping","time":1753379624}
01:53:56.943 player 1 (::1) connected
01:53:56.944 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:53:56.944 [37m[players] <-[32m {"type":"playerCount","count":1}
01:53:56.958 player 2 (::1) connected
01:53:56.959 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:53:56.959 [37m[players] <-[32m {"type":"playerCount","count":2}
01:53:56.973 player 3 (::1) connected
01:53:56.974 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:53:56.974 [37m[players] <-[32m {"type":"playerCount","count":3}
01:53:56.989 player 4 (::1) connected
01:53:56.990 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:53:56.990 [37m[players] <-[32m {"type":"playerCount","count":4}
01:53:57.004 player 5 (::1) connected
01:53:57.004 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:53:57.004 [37m[players] <-[32m {"type":"playerCount","count":5}
01:53:57.072 [37m5 ->[34m {"type":"listStreamers"}
01:53:57.073 [37m5 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:53:57.081 [37m5 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:53:57.081 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"5","dataChannel":true,"sfu":false,"sendOffer":true}
01:53:57.177 [37mStreamer Component -> 5[36m {"type":"offer","playerId":5,"sdp":"v=0\r\no=- 2786935546980864661 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:UA/V\r\na=ice-pwd:YJc4O4tzf7AWzRB4+nBVGfJ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8F:D0:CF:7B:60:66:0D:E1:31:29:8F:9D:5C:BD:D1:0D:1A:13:0A:45:1A:8E:23:23:8E:4D:AB:FC:44:EA:7B:29\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3818356761 1574806561\r\na=ssrc:3818356761 cname:voyjoFfpzR/I4MKX\r\na=ssrc:3818356761 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1574806561 cname:voyjoFfpzR/I4MKX\r\na=ssrc:1574806561 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:UA/V\r\na=ice-pwd:YJc4O4tzf7AWzRB4+nBVGfJ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8F:D0:CF:7B:60:66:0D:E1:31:29:8F:9D:5C:BD:D1:0D:1A:13:0A:45:1A:8E:23:23:8E:4D:AB:FC:44:EA:7B:29\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3832352458 cname:voyjoFfpzR/I4MKX\r\na=ssrc:3832352458 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:UA/V\r\na=ice-pwd:YJc4O4tzf7AWzRB4+nBVGfJ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8F:D0:CF:7B:60:66:0D:E1:31:29:8F:9D:5C:BD:D1:0D:1A:13:0A:45:1A:8E:23:23:8E:4D:AB:FC:44:EA:7B:29\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:53:57.179 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3450464777 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag UA/V network-id 1"}}
01:53:57.210 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1307823946 1 udp 2122194687 *********** 49152 typ host generation 0 ufrag UA/V network-id 4"}}
01:53:57.226 [37m5 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 761432055883711482 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:s2oX\r\na=ice-pwd:JspAH2qe18MWqXiZthmxv6Vg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D7:D2:7D:16:33:A0:D2:D7:F4:D5:41:E9:5A:C0:53:62:CA:0C:4D:1D:4F:A7:81:E4:81:A8:79:52:AA:C7:A7:15\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:s2oX\r\na=ice-pwd:JspAH2qe18MWqXiZthmxv6Vg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D7:D2:7D:16:33:A0:D2:D7:F4:D5:41:E9:5A:C0:53:62:CA:0C:4D:1D:4F:A7:81:E4:81:A8:79:52:AA:C7:A7:15\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 2b9a2f0a-ea5c-4319-b1a2-e4a1bd7139b4\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2546430218 cname:CMackO6gcpiHgpid\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:s2oX\r\na=ice-pwd:JspAH2qe18MWqXiZthmxv6Vg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D7:D2:7D:16:33:A0:D2:D7:F4:D5:41:E9:5A:C0:53:62:CA:0C:4D:1D:4F:A7:81:E4:81:A8:79:52:AA:C7:A7:15\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:53:57.229 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1761523965 1 udp 2122260223 *********** 57531 typ host generation 0 ufrag s2oX network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"s2oX"}}
01:53:57.229 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2404623613 1 udp 2122194687 ************* 57532 typ host generation 0 ufrag s2oX network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"s2oX"}}
01:53:57.243 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3450464777 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag UA/V network-id 1"}}
01:53:57.277 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1307823946 1 udp 2122194687 *********** 49153 typ host generation 0 ufrag UA/V network-id 4"}}
01:53:57.311 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3450464777 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag UA/V network-id 1"}}
01:53:57.344 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1307823946 1 udp 2122194687 *********** 49154 typ host generation 0 ufrag UA/V network-id 4"}}
01:53:57.378 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3009808529 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag UA/V network-id 1"}}
01:53:57.411 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:859581906 1 tcp 1518214911 *********** 49152 typ host tcptype passive generation 0 ufrag UA/V network-id 4"}}
01:54:04.908 player 1 connection closed: 1001 - 
01:54:04.909 [37m[players] <-[32m {"type":"playerCount","count":4}
01:54:04.914 player 5 connection closed: 1006 - 
01:54:04.915 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"5"}
01:54:04.916 [37m[players] <-[32m {"type":"playerCount","count":3}
01:54:04.917 player 4 connection closed: 1006 - 
01:54:04.917 [37m[players] <-[32m {"type":"playerCount","count":2}
01:54:04.918 player 3 connection closed: 1006 - 
01:54:04.918 [37m[players] <-[32m {"type":"playerCount","count":1}
01:54:04.919 player 2 connection closed: 1006 - 
01:54:04.919 [37m[players] <-[32m {"type":"playerCount","count":0}
01:54:10.445 player 6 (::1) connected
01:54:10.446 [37m6 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:54:10.447 [37m[players] <-[32m {"type":"playerCount","count":1}
01:54:10.447 [37m6 ->[34m {"type":"listStreamers"}
01:54:10.448 [37m6 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:54:10.489 [37m6 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:54:10.490 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"6","dataChannel":true,"sfu":false,"sendOffer":true}
01:54:10.548 [37mStreamer Component -> 6[36m {"type":"offer","playerId":6,"sdp":"v=0\r\no=- 4111005361076713366 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iNaq\r\na=ice-pwd:+htBYBnBkWb+MSwNI2tIQfT8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9F:05:23:69:D9:3E:6D:8D:F4:89:A5:45:A8:4A:56:7C:8D:3F:55:D8:FA:D5:41:C2:22:F4:79:BF:48:5D:CD:F7\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 244659261 2209916117\r\na=ssrc:244659261 cname:W/RtnbAcAuV6N46w\r\na=ssrc:244659261 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2209916117 cname:W/RtnbAcAuV6N46w\r\na=ssrc:2209916117 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iNaq\r\na=ice-pwd:+htBYBnBkWb+MSwNI2tIQfT8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9F:05:23:69:D9:3E:6D:8D:F4:89:A5:45:A8:4A:56:7C:8D:3F:55:D8:FA:D5:41:C2:22:F4:79:BF:48:5D:CD:F7\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:619894774 cname:W/RtnbAcAuV6N46w\r\na=ssrc:619894774 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:iNaq\r\na=ice-pwd:+htBYBnBkWb+MSwNI2tIQfT8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9F:05:23:69:D9:3E:6D:8D:F4:89:A5:45:A8:4A:56:7C:8D:3F:55:D8:FA:D5:41:C2:22:F4:79:BF:48:5D:CD:F7\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:10.582 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2269784228 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag iNaq network-id 1"}}
01:54:10.584 [37m6 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5596325386364606436 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TdPc\r\na=ice-pwd:0mpY/O+dgN0CuImNc8Cygj1d\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B4:B5:F4:7D:FC:40:30:4F:34:75:E6:D9:9D:17:C7:47:0D:35:2F:97:BB:9D:D8:D9:45:1D:28:10:AA:C1:8A:AD\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TdPc\r\na=ice-pwd:0mpY/O+dgN0CuImNc8Cygj1d\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B4:B5:F4:7D:FC:40:30:4F:34:75:E6:D9:9D:17:C7:47:0D:35:2F:97:BB:9D:D8:D9:45:1D:28:10:AA:C1:8A:AD\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- b5f8ec96-ef2c-4e4d-8108-39c35d5b2c17\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3933214972 cname:0Fizi/xa73U7iXPG\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:TdPc\r\na=ice-pwd:0mpY/O+dgN0CuImNc8Cygj1d\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B4:B5:F4:7D:FC:40:30:4F:34:75:E6:D9:9D:17:C7:47:0D:35:2F:97:BB:9D:D8:D9:45:1D:28:10:AA:C1:8A:AD\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:10.586 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3530515597 1 udp 2122260223 *********** 55010 typ host generation 0 ufrag TdPc network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TdPc"}}
01:54:10.587 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3344851843 1 udp 2122194687 ************* 55011 typ host generation 0 ufrag TdPc network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TdPc"}}
01:54:10.615 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1746887339 1 udp 2122194687 *********** 49152 typ host generation 0 ufrag iNaq network-id 4"}}
01:54:10.649 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2269784228 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag iNaq network-id 1"}}
01:54:10.683 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1746887339 1 udp 2122194687 *********** 49153 typ host generation 0 ufrag iNaq network-id 4"}}
01:54:10.717 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2269784228 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag iNaq network-id 1"}}
01:54:10.750 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1746887339 1 udp 2122194687 *********** 49154 typ host generation 0 ufrag iNaq network-id 4"}}
01:54:10.784 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2044787760 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag iNaq network-id 1"}}
01:54:10.817 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2528491071 1 tcp 1518214911 *********** 49152 typ host tcptype passive generation 0 ufrag iNaq network-id 4"}}
01:54:10.851 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2044787760 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag iNaq network-id 1"}}
01:54:10.884 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2528491071 1 tcp 1518214911 *********** 49153 typ host tcptype passive generation 0 ufrag iNaq network-id 4"}}
01:54:10.918 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2044787760 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag iNaq network-id 1"}}
01:54:10.952 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2528491071 1 tcp 1518214911 *********** 49154 typ host tcptype passive generation 0 ufrag iNaq network-id 4"}}
01:54:14.978 player 6 connection closed: 1001 - 
01:54:14.980 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"6"}
01:54:14.980 [37m[players] <-[32m {"type":"playerCount","count":0}
01:54:15.798 player 7 (::1) connected
01:54:15.799 [37m7 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:54:15.799 [37m[players] <-[32m {"type":"playerCount","count":1}
01:54:15.799 [37m7 ->[34m {"type":"listStreamers"}
01:54:15.800 [37m7 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:54:15.826 [37m7 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:54:15.827 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"7","dataChannel":true,"sfu":false,"sendOffer":true}
01:54:15.875 [37mStreamer Component -> 7[36m {"type":"offer","playerId":7,"sdp":"v=0\r\no=- 3433221579228636871 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:b4ZM\r\na=ice-pwd:K8uUkMfryqmkiShR9bAS8yD8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 79:E2:B2:A1:11:6D:95:87:A4:07:C1:9F:BD:F6:59:63:9E:5F:46:50:34:10:26:B7:58:2D:80:5F:39:CD:CB:9D\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2701642382 1473374015\r\na=ssrc:2701642382 cname:j4Y9FaN9eF69JxCA\r\na=ssrc:2701642382 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1473374015 cname:j4Y9FaN9eF69JxCA\r\na=ssrc:1473374015 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:b4ZM\r\na=ice-pwd:K8uUkMfryqmkiShR9bAS8yD8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 79:E2:B2:A1:11:6D:95:87:A4:07:C1:9F:BD:F6:59:63:9E:5F:46:50:34:10:26:B7:58:2D:80:5F:39:CD:CB:9D\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2555745657 cname:j4Y9FaN9eF69JxCA\r\na=ssrc:2555745657 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:b4ZM\r\na=ice-pwd:K8uUkMfryqmkiShR9bAS8yD8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 79:E2:B2:A1:11:6D:95:87:A4:07:C1:9F:BD:F6:59:63:9E:5F:46:50:34:10:26:B7:58:2D:80:5F:39:CD:CB:9D\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:15.905 [37m7 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6321140319236342320 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qyIS\r\na=ice-pwd:mG05lZjIi0mK/9WRcYRZn/17\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:28:73:A6:2D:BD:78:01:30:82:99:B5:A8:7D:45:74:78:31:82:92:C4:4C:11:9C:9C:29:51:61:CB:4E:32:0C\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qyIS\r\na=ice-pwd:mG05lZjIi0mK/9WRcYRZn/17\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:28:73:A6:2D:BD:78:01:30:82:99:B5:A8:7D:45:74:78:31:82:92:C4:4C:11:9C:9C:29:51:61:CB:4E:32:0C\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 4b526e44-ffc2-4eaf-9f31-08de8128b4e3\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2803865102 cname:fLA+QiMoEobl6frx\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:qyIS\r\na=ice-pwd:mG05lZjIi0mK/9WRcYRZn/17\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:28:73:A6:2D:BD:78:01:30:82:99:B5:A8:7D:45:74:78:31:82:92:C4:4C:11:9C:9C:29:51:61:CB:4E:32:0C\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:15.907 [37m7 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1700503902 1 udp 2122260223 *********** 60332 typ host generation 0 ufrag qyIS network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"qyIS"}}
01:54:15.907 [37m7 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1886037584 1 udp 2122194687 ************* 60333 typ host generation 0 ufrag qyIS network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"qyIS"}}
01:54:15.908 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3670030205 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag b4ZM network-id 1"}}
01:54:15.941 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:898989426 1 udp 2122194687 *********** 49152 typ host generation 0 ufrag b4ZM network-id 4"}}
01:54:15.974 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3670030205 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag b4ZM network-id 1"}}
01:54:16.008 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:898989426 1 udp 2122194687 *********** 49153 typ host generation 0 ufrag b4ZM network-id 4"}}
01:54:16.041 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3670030205 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag b4ZM network-id 1"}}
01:54:16.075 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:898989426 1 udp 2122194687 *********** 49154 typ host generation 0 ufrag b4ZM network-id 4"}}
01:54:20.779 player 7 connection closed: 1001 - 
01:54:20.780 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"7"}
01:54:20.781 [37m[players] <-[32m {"type":"playerCount","count":0}
01:54:21.760 player 8 (::1) connected
01:54:21.761 [37m8 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:54:21.761 [37m[players] <-[32m {"type":"playerCount","count":1}
01:54:21.763 [37m8 ->[34m {"type":"listStreamers"}
01:54:21.763 [37m8 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:54:21.788 [37m8 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:54:21.789 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"8","dataChannel":true,"sfu":false,"sendOffer":true}
01:54:21.847 [37mStreamer Component -> 8[36m {"type":"offer","playerId":8,"sdp":"v=0\r\no=- 2398925237881196054 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zEmL\r\na=ice-pwd:LofToGZ8Z/ByeTP80YlqOK2x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:0C:44:95:76:6C:33:C2:E1:CD:10:5A:61:80:34:92:C9:0A:29:A8:45:C0:48:64:77:F6:87:66:1D:62:68:1E\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2709170543 2531941522\r\na=ssrc:2709170543 cname:AZBxITEoXVm9zw7u\r\na=ssrc:2709170543 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2531941522 cname:AZBxITEoXVm9zw7u\r\na=ssrc:2531941522 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zEmL\r\na=ice-pwd:LofToGZ8Z/ByeTP80YlqOK2x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:0C:44:95:76:6C:33:C2:E1:CD:10:5A:61:80:34:92:C9:0A:29:A8:45:C0:48:64:77:F6:87:66:1D:62:68:1E\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1058591932 cname:AZBxITEoXVm9zw7u\r\na=ssrc:1058591932 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:zEmL\r\na=ice-pwd:LofToGZ8Z/ByeTP80YlqOK2x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:0C:44:95:76:6C:33:C2:E1:CD:10:5A:61:80:34:92:C9:0A:29:A8:45:C0:48:64:77:F6:87:66:1D:62:68:1E\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:21.874 [37m8 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2987141528521901862 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ZCQF\r\na=ice-pwd:eMkp89f9iQCDgJKQSwYGNyq5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1E:DB:31:48:97:F6:73:F1:AB:FC:9C:2E:95:EF:5E:4F:FF:C8:72:3B:B8:2F:C3:57:AE:49:35:1C:2D:FD:8C:3D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ZCQF\r\na=ice-pwd:eMkp89f9iQCDgJKQSwYGNyq5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1E:DB:31:48:97:F6:73:F1:AB:FC:9C:2E:95:EF:5E:4F:FF:C8:72:3B:B8:2F:C3:57:AE:49:35:1C:2D:FD:8C:3D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- da9b7e92-d683-43c7-8919-c01eae7a6d3e\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2345016941 cname:XtWw3+LDFgunNnit\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:ZCQF\r\na=ice-pwd:eMkp89f9iQCDgJKQSwYGNyq5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1E:DB:31:48:97:F6:73:F1:AB:FC:9C:2E:95:EF:5E:4F:FF:C8:72:3B:B8:2F:C3:57:AE:49:35:1C:2D:FD:8C:3D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:54:21.875 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:757811764 1 udp 2122260223 *********** 56968 typ host generation 0 ufrag ZCQF network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"ZCQF"}}
01:54:21.875 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:941247802 1 udp 2122194687 ************* 56969 typ host generation 0 ufrag ZCQF network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"ZCQF"}}
01:54:21.880 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3919455367 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag zEmL network-id 1"}}
01:54:21.914 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1774453188 1 udp 2122194687 *********** 49152 typ host generation 0 ufrag zEmL network-id 4"}}
01:54:21.947 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3919455367 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag zEmL network-id 1"}}
01:54:21.981 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1774453188 1 udp 2122194687 *********** 49153 typ host generation 0 ufrag zEmL network-id 4"}}
01:54:22.015 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3919455367 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag zEmL network-id 1"}}
01:54:22.048 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1774453188 1 udp 2122194687 *********** 49154 typ host generation 0 ufrag zEmL network-id 4"}}
01:54:22.082 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2538718751 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag zEmL network-id 1"}}
01:54:22.116 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:386655068 1 tcp 1518214911 *********** 49152 typ host tcptype passive generation 0 ufrag zEmL network-id 4"}}
01:54:44.989 [37mStreamer Component ->[34m {"type":"ping","time":1753379684}
01:55:45.014 [37mStreamer Component ->[34m {"type":"ping","time":1753379744}
01:56:45.006 [37mStreamer Component ->[34m {"type":"ping","time":1753379804}
01:57:45.016 [37mStreamer Component ->[34m {"type":"ping","time":1753379864}
01:58:44.996 [37mStreamer Component ->[34m {"type":"ping","time":1753379924}
01:59:44.983 [37mStreamer Component ->[34m {"type":"ping","time":1753379984}
02:00:45.019 [37mStreamer Component ->[34m {"type":"ping","time":1753380045}
02:01:44.985 [37mStreamer Component ->[34m {"type":"ping","time":1753380104}
02:02:45.029 [37mStreamer Component ->[34m {"type":"ping","time":1753380164}
02:03:45.006 [37mStreamer Component ->[34m {"type":"ping","time":1753380224}
02:04:44.974 [37mStreamer Component ->[34m {"type":"ping","time":1753380284}
02:05:33.807 player 8 connection closed: 1001 - 
02:05:33.812 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"8"}
02:05:33.813 [37m[players] <-[32m {"type":"playerCount","count":0}
02:05:45.020 [37mStreamer Component ->[34m {"type":"ping","time":1753380344}
02:05:46.176 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
02:05:46.177 unsubscribing all players on Streamer Component
