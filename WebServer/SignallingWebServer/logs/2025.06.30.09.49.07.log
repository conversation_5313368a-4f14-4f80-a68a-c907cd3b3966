09:49:07.817 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
09:49:07.857 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
09:49:07.859 Redirecting http->https
09:49:07.864 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
09:49:07.871 WebSocket listening for Streamer connections on :8888
09:49:07.872 WebSocket listening for SFU connections on :8889
09:49:07.872 WebSocket listening for Players connections on :80
09:49:07.873 Http listening on *: 80
09:49:07.873 Https listening on *: 443
09:50:44.503 Streamer connected: ::1
09:50:44.504 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:50:44.504 [37m::1 <-[32m {"type":"identify"}
09:50:44.998 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:50:44.998 Registered new streamer: Streamer Component
09:51:48.102 [37mStreamer Component ->[34m {"type":"ping","time":1753840308}
