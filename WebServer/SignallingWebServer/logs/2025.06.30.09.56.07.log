09:56:07.460 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
09:56:07.501 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
09:56:07.503 Redirecting http->https
09:56:07.509 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
09:56:07.516 WebSocket listening for Streamer connections on :8888
09:56:07.516 WebSocket listening for SFU connections on :8889
09:56:07.517 WebSocket listening for Players connections on :80
09:56:07.518 Http listening on *: 80
09:56:07.518 Https listening on *: 443
09:57:37.962 Streamer connected: ::1
09:57:37.963 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:57:37.964 [37m::1 <-[32m {"type":"identify"}
09:57:38.388 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:57:38.388 Registered new streamer: Streamer Component
09:58:38.457 [37mStreamer Component ->[34m {"type":"ping","time":1753840718}
09:58:38.613 player 1 (::1) connected
09:58:38.613 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:58:38.614 [37m[players] <-[32m {"type":"playerCount","count":1}
09:58:38.614 [37m1 ->[34m {"type":"listStreamers"}
09:58:38.615 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:58:38.640 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:58:38.641 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
09:58:38.728 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 1606152527508475920 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uJva\r\na=ice-pwd:qaNpYwHCZLfW0ZgIvYrrvo91\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A8:47:40:2E:44:BA:9E:13:60:76:B9:5A:64:17:32:90:A3:32:E3:AB:1F:28:4E:38:4E:15:AB:4A:4B:25:45:71\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2886815206 3626082503\r\na=ssrc:2886815206 cname:wc3aYnxDWRsCv8z8\r\na=ssrc:2886815206 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3626082503 cname:wc3aYnxDWRsCv8z8\r\na=ssrc:3626082503 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uJva\r\na=ice-pwd:qaNpYwHCZLfW0ZgIvYrrvo91\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A8:47:40:2E:44:BA:9E:13:60:76:B9:5A:64:17:32:90:A3:32:E3:AB:1F:28:4E:38:4E:15:AB:4A:4B:25:45:71\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2653351069 cname:wc3aYnxDWRsCv8z8\r\na=ssrc:2653351069 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uJva\r\na=ice-pwd:qaNpYwHCZLfW0ZgIvYrrvo91\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A8:47:40:2E:44:BA:9E:13:60:76:B9:5A:64:17:32:90:A3:32:E3:AB:1F:28:4E:38:4E:15:AB:4A:4B:25:45:71\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:58:38.729 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1140168037 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag uJva network-id 1"}}
09:58:38.760 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2588755450 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag uJva network-id 2"}}
09:58:38.770 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5315339131291959232 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:5e6/\r\na=ice-pwd:uboWZcVOjVGtdN97NyJrkaTM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:33:B6:A5:56:B7:25:49:4A:8F:89:9A:3D:03:D7:B1:42:DC:6A:4D:0E:7E:1D:60:C4:74:49:DD:B2:C2:A3:1F\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:5e6/\r\na=ice-pwd:uboWZcVOjVGtdN97NyJrkaTM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:33:B6:A5:56:B7:25:49:4A:8F:89:9A:3D:03:D7:B1:42:DC:6A:4D:0E:7E:1D:60:C4:74:49:DD:B2:C2:A3:1F\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 25397cb1-19d2-4e56-ac29-d9b9a62e0695\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2193201536 cname:qLR9CF7FLIIalFea\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:5e6/\r\na=ice-pwd:uboWZcVOjVGtdN97NyJrkaTM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:33:B6:A5:56:B7:25:49:4A:8F:89:9A:3D:03:D7:B1:42:DC:6A:4D:0E:7E:1D:60:C4:74:49:DD:B2:C2:A3:1F\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:58:38.770 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1096476377 1 udp 2122260223 *********** 53492 typ host generation 0 ufrag 5e6/ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"5e6/"}}
09:58:38.771 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3025728145 1 udp 2122194687 ************* 53493 typ host generation 0 ufrag 5e6/ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"5e6/"}}
09:58:38.794 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1140168037 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag uJva network-id 1"}}
09:58:38.828 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2588755450 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag uJva network-id 2"}}
09:58:38.861 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1140168037 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag uJva network-id 1"}}
09:58:38.895 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2588755450 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag uJva network-id 2"}}
09:59:38.488 [37mStreamer Component ->[34m {"type":"ping","time":1753840778}
10:00:38.452 [37mStreamer Component ->[34m {"type":"ping","time":1753840838}
10:01:38.469 [37mStreamer Component ->[34m {"type":"ping","time":1753840898}
10:02:38.524 [37mStreamer Component ->[34m {"type":"ping","time":1753840958}
10:03:26.169 player 1 connection closed: 1001 - 
10:03:26.171 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
10:03:26.171 [37m[players] <-[32m {"type":"playerCount","count":0}
10:03:28.272 player 2 (::1) connected
10:03:28.273 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:03:28.273 [37m[players] <-[32m {"type":"playerCount","count":1}
10:03:28.275 [37m2 ->[34m {"type":"listStreamers"}
10:03:28.275 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:03:28.300 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:03:28.301 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
10:03:28.356 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 7444043428401805803 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:8QXn\r\na=ice-pwd:2Ktqi0wiOADpfX2/Me2yv5UM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:49:6E:49:96:72:22:E9:39:C3:17:90:2E:78:FC:04:1A:BB:AD:75:4B:CA:AB:AD:C0:D3:3E:BA:C1:22:D3:87\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1613709404 1951771626\r\na=ssrc:1613709404 cname:BoOFYocvBTAVK5dk\r\na=ssrc:1613709404 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1951771626 cname:BoOFYocvBTAVK5dk\r\na=ssrc:1951771626 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:8QXn\r\na=ice-pwd:2Ktqi0wiOADpfX2/Me2yv5UM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:49:6E:49:96:72:22:E9:39:C3:17:90:2E:78:FC:04:1A:BB:AD:75:4B:CA:AB:AD:C0:D3:3E:BA:C1:22:D3:87\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2000766392 cname:BoOFYocvBTAVK5dk\r\na=ssrc:2000766392 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:8QXn\r\na=ice-pwd:2Ktqi0wiOADpfX2/Me2yv5UM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:49:6E:49:96:72:22:E9:39:C3:17:90:2E:78:FC:04:1A:BB:AD:75:4B:CA:AB:AD:C0:D3:3E:BA:C1:22:D3:87\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:03:28.389 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4279511361 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag 8QXn network-id 1"}}
10:03:28.396 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5494751580030964426 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:o8vH\r\na=ice-pwd:cEMkyMQURW2ZPAg/nOsrZCJM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:0D:FF:A8:A3:B0:15:3C:A6:E4:EA:A7:8F:62:75:C8:A6:84:56:D9:ED:A3:44:8E:A4:6E:C8:0E:3D:4F:28:3D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:o8vH\r\na=ice-pwd:cEMkyMQURW2ZPAg/nOsrZCJM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:0D:FF:A8:A3:B0:15:3C:A6:E4:EA:A7:8F:62:75:C8:A6:84:56:D9:ED:A3:44:8E:A4:6E:C8:0E:3D:4F:28:3D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- e4a2c835-2e03-474c-9d50-9d848920df65\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1383900843 cname:gBcO5KnSVmZjj4Sj\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:o8vH\r\na=ice-pwd:cEMkyMQURW2ZPAg/nOsrZCJM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:0D:FF:A8:A3:B0:15:3C:A6:E4:EA:A7:8F:62:75:C8:A6:84:56:D9:ED:A3:44:8E:A4:6E:C8:0E:3D:4F:28:3D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:03:28.402 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2374266028 1 udp 2122260223 *********** 50497 typ host generation 0 ufrag o8vH network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"o8vH"}}
10:03:28.403 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2022078692 1 udp 2122194687 ************* 50498 typ host generation 0 ufrag o8vH network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"o8vH"}}
10:03:28.424 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2675777348 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag 8QXn network-id 2"}}
10:03:28.456 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4279511361 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag 8QXn network-id 1"}}
10:03:28.490 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2675777348 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag 8QXn network-id 2"}}
10:03:28.523 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4279511361 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag 8QXn network-id 1"}}
10:03:28.557 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2675777348 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag 8QXn network-id 2"}}
10:03:38.481 [37mStreamer Component ->[34m {"type":"ping","time":1753841018}
10:04:38.499 [37mStreamer Component ->[34m {"type":"ping","time":1753841078}
10:05:38.485 [37mStreamer Component ->[34m {"type":"ping","time":1753841138}
10:06:38.477 [37mStreamer Component ->[34m {"type":"ping","time":1753841198}
10:07:38.516 [37mStreamer Component ->[34m {"type":"ping","time":1753841258}
10:08:38.526 [37mStreamer Component ->[34m {"type":"ping","time":1753841318}
10:09:38.467 [37mStreamer Component ->[34m {"type":"ping","time":1753841378}
10:10:38.455 [37mStreamer Component ->[34m {"type":"ping","time":1753841438}
10:11:38.494 [37mStreamer Component ->[34m {"type":"ping","time":1753841498}
10:11:54.448 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:11:54.449 unsubscribing all players on Streamer Component
10:11:54.450 player 2 connection closed: 1005 - 
10:11:54.450 [37m[players] <-[32m {"type":"playerCount","count":0}
