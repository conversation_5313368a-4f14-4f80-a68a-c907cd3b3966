10:12:08.630 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:12:08.673 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:12:08.674 Redirecting http->https
10:12:08.679 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:12:08.686 WebSocket listening for Streamer connections on :8888
10:12:08.687 WebSocket listening for SFU connections on :8889
10:12:08.688 WebSocket listening for Players connections on :80
10:12:08.689 Http listening on *: 80
10:12:08.689 Https listening on *: 443
