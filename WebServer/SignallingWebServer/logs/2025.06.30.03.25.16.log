03:25:16.946 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
03:25:16.984 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
03:25:16.986 Redirecting http->https
03:25:16.991 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
03:25:16.998 WebSocket listening for Streamer connections on :8888
03:25:16.999 WebSocket listening for SFU connections on :8889
03:25:16.999 WebSocket listening for Players connections on :80
03:25:17.000 Http listening on *: 80
03:25:17.001 Https listening on *: 443
03:26:59.573 Streamer connected: ::1
03:26:59.574 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:26:59.575 [37m::1 <-[32m {"type":"identify"}
03:27:00.110 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
03:27:00.111 Registered new streamer: Streamer Component
03:28:00.190 [37mStreamer Component ->[34m {"type":"ping","time":1753817280}
03:28:41.716 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
03:28:41.717 unsubscribing all players on Streamer Component
03:29:02.713 Streamer connected: ::1
03:29:02.713 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:29:02.714 [37m::1 <-[32m {"type":"identify"}
03:29:03.217 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
03:29:03.218 Registered new streamer: Streamer Component
03:29:16.097 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
03:29:16.097 unsubscribing all players on Streamer Component
03:29:52.831 Streamer connected: ::1
03:29:52.832 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:29:52.832 [37m::1 <-[32m {"type":"identify"}
03:29:53.336 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
03:29:53.337 Registered new streamer: Streamer Component
03:30:53.413 [37mStreamer Component ->[34m {"type":"ping","time":1753817453}
03:31:53.424 [37mStreamer Component ->[34m {"type":"ping","time":1753817513}
03:32:53.406 [37mStreamer Component ->[34m {"type":"ping","time":1753817573}
03:33:53.397 [37mStreamer Component ->[34m {"type":"ping","time":1753817633}
03:34:53.415 [37mStreamer Component ->[34m {"type":"ping","time":1753817693}
03:35:53.450 [37mStreamer Component ->[34m {"type":"ping","time":1753817753}
03:36:53.420 [37mStreamer Component ->[34m {"type":"ping","time":1753817813}
03:37:12.424 player 1 (::1) connected
03:37:12.425 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:37:12.426 [37m[players] <-[32m {"type":"playerCount","count":1}
03:37:12.427 [37m1 ->[34m {"type":"listStreamers"}
03:37:12.427 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:37:12.457 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:37:12.458 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
03:37:12.585 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 8575268415250495180 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:I1P5\r\na=ice-pwd:SDRyAhsVyUwbHXMzsPU6n14p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5D:7E:8F:23:3B:75:E0:63:2A:17:A3:40:1E:78:60:E5:FE:69:85:5B:70:A1:25:54:EA:E6:D3:45:5D:2D:25:C9\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 630833507 2528787191\r\na=ssrc:630833507 cname:JrMWRoYpMw8Hgvnc\r\na=ssrc:630833507 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2528787191 cname:JrMWRoYpMw8Hgvnc\r\na=ssrc:2528787191 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:I1P5\r\na=ice-pwd:SDRyAhsVyUwbHXMzsPU6n14p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5D:7E:8F:23:3B:75:E0:63:2A:17:A3:40:1E:78:60:E5:FE:69:85:5B:70:A1:25:54:EA:E6:D3:45:5D:2D:25:C9\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3788796711 cname:JrMWRoYpMw8Hgvnc\r\na=ssrc:3788796711 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:I1P5\r\na=ice-pwd:SDRyAhsVyUwbHXMzsPU6n14p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5D:7E:8F:23:3B:75:E0:63:2A:17:A3:40:1E:78:60:E5:FE:69:85:5B:70:A1:25:54:EA:E6:D3:45:5D:2D:25:C9\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:37:12.616 [37m1 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 8772891949427387521 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RdQu\r\na=ice-pwd:A8Qxy0CQ5pzuJqr4uzTZVvcW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:8F:9E:F8:70:95:B8:CF:63:F8:E1:22:C5:29:6C:0B:1E:26:BB:9A:6D:E2:4D:1B:80:09:3D:19:5E:E1:E8:01\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RdQu\r\na=ice-pwd:A8Qxy0CQ5pzuJqr4uzTZVvcW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:8F:9E:F8:70:95:B8:CF:63:F8:E1:22:C5:29:6C:0B:1E:26:BB:9A:6D:E2:4D:1B:80:09:3D:19:5E:E1:E8:01\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 20a1ca4c-ea46-4d81-93f7-81540c0043b8\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:80823139 cname:LU5M45qfUcScznY2\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:RdQu\r\na=ice-pwd:A8Qxy0CQ5pzuJqr4uzTZVvcW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:8F:9E:F8:70:95:B8:CF:63:F8:E1:22:C5:29:6C:0B:1E:26:BB:9A:6D:E2:4D:1B:80:09:3D:19:5E:E1:E8:01\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:37:12.616 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1742334938 1 udp ********** *********** 54931 typ host generation 0 ufrag RdQu network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RdQu"}}
03:37:12.616 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2463891346 1 udp ********** ************* 54932 typ host generation 0 ufrag RdQu network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RdQu"}}
03:37:12.618 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1924794411 1 udp ********** *********** 49152 typ host generation 0 ufrag I1P5 network-id 1"}}
03:37:12.653 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:315833902 1 udp ********** ************* 49152 typ host generation 0 ufrag I1P5 network-id 2"}}
03:37:12.687 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1924794411 1 udp ********** *********** 49153 typ host generation 0 ufrag I1P5 network-id 1"}}
03:37:12.721 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:315833902 1 udp ********** ************* 49153 typ host generation 0 ufrag I1P5 network-id 2"}}
03:37:12.754 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1924794411 1 udp ********** *********** 49154 typ host generation 0 ufrag I1P5 network-id 1"}}
03:37:12.788 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:315833902 1 udp ********** ************* 49154 typ host generation 0 ufrag I1P5 network-id 2"}}
03:37:13.084 player 1 connection closed: 1001 - 
03:37:13.085 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
03:37:13.087 [37m[players] <-[32m {"type":"playerCount","count":0}
03:37:53.670 [37mStreamer Component ->[34m {"type":"ping","time":1753817873}
03:38:16.239 player 2 (::1) connected
03:38:16.240 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:38:16.241 [37m[players] <-[32m {"type":"playerCount","count":1}
03:38:16.241 [37m2 ->[34m {"type":"listStreamers"}
03:38:16.241 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:38:16.350 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:38:16.351 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
03:38:16.397 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 1969523847976001654 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:GGQi\r\na=ice-pwd:xNXttpZXlS5NkcY9V47N2c9U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A3:7B:6F:33:35:0C:17:44:50:78:CD:77:95:44:92:19:66:9F:85:84:D1:7F:17:E9:8E:D1:C5:06:1E:FC:CE:43\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1182817340 2882478669\r\na=ssrc:1182817340 cname:IXqPinTtoRAPxLPC\r\na=ssrc:1182817340 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2882478669 cname:IXqPinTtoRAPxLPC\r\na=ssrc:2882478669 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:GGQi\r\na=ice-pwd:xNXttpZXlS5NkcY9V47N2c9U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A3:7B:6F:33:35:0C:17:44:50:78:CD:77:95:44:92:19:66:9F:85:84:D1:7F:17:E9:8E:D1:C5:06:1E:FC:CE:43\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2021359684 cname:IXqPinTtoRAPxLPC\r\na=ssrc:2021359684 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:GGQi\r\na=ice-pwd:xNXttpZXlS5NkcY9V47N2c9U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A3:7B:6F:33:35:0C:17:44:50:78:CD:77:95:44:92:19:66:9F:85:84:D1:7F:17:E9:8E:D1:C5:06:1E:FC:CE:43\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:38:16.431 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3544329333 1 udp ********** *********** 49152 typ host generation 0 ufrag GGQi network-id 1"}}
03:38:16.460 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2801519881828088479 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kB3O\r\na=ice-pwd:/j2zkdXzOAVSO49zQ5JbYkON\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:86:31:3E:AD:D8:8D:E6:DF:BE:02:1E:20:3E:1F:AA:A7:8A:5E:0F:CB:60:EC:CD:11:5D:07:75:76:A6:D9:76\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kB3O\r\na=ice-pwd:/j2zkdXzOAVSO49zQ5JbYkON\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:86:31:3E:AD:D8:8D:E6:DF:BE:02:1E:20:3E:1F:AA:A7:8A:5E:0F:CB:60:EC:CD:11:5D:07:75:76:A6:D9:76\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- c3f825ba-310f-4262-a2c3-f7dd806c45a3\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2442911966 cname:TmsGAzrSZtLQfkyL\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:kB3O\r\na=ice-pwd:/j2zkdXzOAVSO49zQ5JbYkON\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:86:31:3E:AD:D8:8D:E6:DF:BE:02:1E:20:3E:1F:AA:A7:8A:5E:0F:CB:60:EC:CD:11:5D:07:75:76:A6:D9:76\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:38:16.463 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3245381129 1 udp ********** *********** 54554 typ host generation 0 ufrag kB3O network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"kB3O"}}
03:38:16.464 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3112328639 1 udp ********** ************* 54555 typ host generation 0 ufrag kB3O network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"kB3O"}}
03:38:16.464 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3005946480 1 udp ********** ************* 49152 typ host generation 0 ufrag GGQi network-id 2"}}
03:38:16.498 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3544329333 1 udp ********** *********** 49153 typ host generation 0 ufrag GGQi network-id 1"}}
03:38:16.532 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3005946480 1 udp ********** ************* 49153 typ host generation 0 ufrag GGQi network-id 2"}}
03:38:16.566 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3544329333 1 udp ********** *********** 49154 typ host generation 0 ufrag GGQi network-id 1"}}
03:38:16.600 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3005946480 1 udp ********** ************* 49154 typ host generation 0 ufrag GGQi network-id 2"}}
03:38:53.620 [37mStreamer Component ->[34m {"type":"ping","time":1753817933}
03:39:53.626 [37mStreamer Component ->[34m {"type":"ping","time":1753817993}
03:40:53.643 [37mStreamer Component ->[34m {"type":"ping","time":1753818053}
03:41:53.652 [37mStreamer Component ->[34m {"type":"ping","time":1753818113}
03:42:53.656 [37mStreamer Component ->[34m {"type":"ping","time":1753818173}
03:43:53.668 [37mStreamer Component ->[34m {"type":"ping","time":1753818233}
03:44:53.643 [37mStreamer Component ->[34m {"type":"ping","time":1753818293}
03:45:53.655 [37mStreamer Component ->[34m {"type":"ping","time":1753818353}
03:46:53.663 [37mStreamer Component ->[34m {"type":"ping","time":1753818413}
03:47:53.619 [37mStreamer Component ->[34m {"type":"ping","time":1753818473}
03:48:53.626 [37mStreamer Component ->[34m {"type":"ping","time":1753818533}
03:49:53.635 [37mStreamer Component ->[34m {"type":"ping","time":1753818593}
03:50:59.308 [37mStreamer Component ->[34m {"type":"ping","time":1753818659}
03:51:59.298 [37mStreamer Component ->[34m {"type":"ping","time":1753818719}
03:53:00.649 [37mStreamer Component ->[34m {"type":"ping","time":1753818780}
03:54:07.202 [37mStreamer Component ->[34m {"type":"ping","time":1753818847}
03:54:55.611 streamer Streamer Component disconnected: 1006 - 
03:54:55.612 unsubscribing all players on Streamer Component
03:54:55.613 player 2 connection closed: 1005 - 
03:54:55.613 [37m[players] <-[32m {"type":"playerCount","count":0}
05:49:54.435 player 3 (::1) connected
05:49:54.436 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:49:54.436 [37m[players] <-[32m {"type":"playerCount","count":1}
05:49:54.436 [37m3 ->[34m {"type":"listStreamers"}
05:49:54.437 [37m3 <-[32m {"type":"streamerList","ids":[]}
05:49:55.638 player 3 connection closed: 1005 - 
05:49:55.639 [37m[players] <-[32m {"type":"playerCount","count":0}
05:49:58.672 player 4 (::1) connected
05:49:58.672 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:49:58.673 [37m[players] <-[32m {"type":"playerCount","count":1}
05:49:58.674 [37m4 ->[34m {"type":"listStreamers"}
05:49:58.674 [37m4 <-[32m {"type":"streamerList","ids":[]}
05:50:49.273 player 4 connection closed: 1001 - 
05:50:49.274 [37m[players] <-[32m {"type":"playerCount","count":0}
05:50:56.937 player 5 (::1) connected
05:50:56.938 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:50:56.938 [37m[players] <-[32m {"type":"playerCount","count":1}
05:50:56.939 [37m5 ->[34m {"type":"listStreamers"}
05:50:56.940 [37m5 <-[32m {"type":"streamerList","ids":[]}
05:50:58.876 player 5 connection closed: 1005 - 
05:50:58.876 [37m[players] <-[32m {"type":"playerCount","count":0}
05:51:01.897 player 6 (::1) connected
05:51:01.899 [37m6 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:51:01.899 [37m[players] <-[32m {"type":"playerCount","count":1}
05:51:01.899 [37m6 ->[34m {"type":"listStreamers"}
05:51:01.899 [37m6 <-[32m {"type":"streamerList","ids":[]}
05:56:56.003 player 7 (::1) connected
05:56:56.004 [37m7 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:56:56.004 [37m[players] <-[32m {"type":"playerCount","count":2}
05:56:56.005 [37m7 ->[34m {"type":"listStreamers"}
05:56:56.005 [37m7 <-[32m {"type":"streamerList","ids":[]}
05:57:19.116 Streamer connected: ::1
05:57:19.117 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:57:19.117 [37m::1 <-[32m {"type":"identify"}
05:57:19.620 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
05:57:19.620 Registered new streamer: Streamer Component
05:58:02.208 player 7 connection closed: 1005 - 
05:58:02.209 [37m[players] <-[32m {"type":"playerCount","count":1}
05:58:05.230 player 8 (::1) connected
05:58:05.231 [37m8 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
05:58:05.231 [37m[players] <-[32m {"type":"playerCount","count":2}
05:58:05.233 [37m8 ->[34m {"type":"listStreamers"}
05:58:05.233 [37m8 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
05:58:05.267 [37m8 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
05:58:05.268 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"8","dataChannel":true,"sfu":false,"sendOffer":true}
05:58:05.477 [37mStreamer Component -> 8[36m {"type":"offer","playerId":8,"sdp":"v=0\r\no=- 3453929324101925077 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:YmnX\r\na=ice-pwd:qDUHXWCm9B0oP1BsAf5nMGQO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A7:37:96:B6:4F:EA:6F:E6:3C:A3:2F:5F:A6:67:08:2C:99:7B:5A:1D:D0:13:94:3D:73:9F:E2:D0:E2:72:85:19\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1743939643 3634819524\r\na=ssrc:1743939643 cname:UUAo49jNUpgGgcbW\r\na=ssrc:1743939643 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3634819524 cname:UUAo49jNUpgGgcbW\r\na=ssrc:3634819524 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:YmnX\r\na=ice-pwd:qDUHXWCm9B0oP1BsAf5nMGQO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A7:37:96:B6:4F:EA:6F:E6:3C:A3:2F:5F:A6:67:08:2C:99:7B:5A:1D:D0:13:94:3D:73:9F:E2:D0:E2:72:85:19\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1449027362 cname:UUAo49jNUpgGgcbW\r\na=ssrc:1449027362 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:YmnX\r\na=ice-pwd:qDUHXWCm9B0oP1BsAf5nMGQO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A7:37:96:B6:4F:EA:6F:E6:3C:A3:2F:5F:A6:67:08:2C:99:7B:5A:1D:D0:13:94:3D:73:9F:E2:D0:E2:72:85:19\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
05:58:05.511 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1414268265 1 udp ********** *********** 49152 typ host generation 0 ufrag YmnX network-id 1"}}
05:58:05.516 [37m8 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8517877958786513657 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uP5f\r\na=ice-pwd:pjO893TG2bxFSjyWhu8rTGgc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 42:27:48:14:3D:E8:6E:BB:28:3A:F3:4D:F4:63:BC:18:C1:D8:7D:98:10:C0:3D:54:9F:87:D5:44:AA:8A:EE:FA\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uP5f\r\na=ice-pwd:pjO893TG2bxFSjyWhu8rTGgc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 42:27:48:14:3D:E8:6E:BB:28:3A:F3:4D:F4:63:BC:18:C1:D8:7D:98:10:C0:3D:54:9F:87:D5:44:AA:8A:EE:FA\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 636b0fa8-44b8-40f7-b0a9-355592ccc249\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1604318654 cname:OxAHnGuvC3Fpacyz\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uP5f\r\na=ice-pwd:pjO893TG2bxFSjyWhu8rTGgc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 42:27:48:14:3D:E8:6E:BB:28:3A:F3:4D:F4:63:BC:18:C1:D8:7D:98:10:C0:3D:54:9F:87:D5:44:AA:8A:EE:FA\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
05:58:05.519 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2602362892 1 udp ********** *********** 54393 typ host generation 0 ufrag uP5f network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uP5f"}}
05:58:05.519 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1847524420 1 udp ********** ************* 54394 typ host generation 0 ufrag uP5f network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uP5f"}}
05:58:05.545 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:608606416 1 udp ********** ************* 49152 typ host generation 0 ufrag YmnX network-id 2"}}
05:58:05.578 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1414268265 1 udp ********** *********** 49153 typ host generation 0 ufrag YmnX network-id 1"}}
05:58:05.613 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:608606416 1 udp ********** ************* 49153 typ host generation 0 ufrag YmnX network-id 2"}}
05:58:05.647 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1414268265 1 udp ********** *********** 49154 typ host generation 0 ufrag YmnX network-id 1"}}
05:58:05.681 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:608606416 1 udp ********** ************* 49154 typ host generation 0 ufrag YmnX network-id 2"}}
05:58:05.715 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2867253757 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag YmnX network-id 1"}}
05:58:05.749 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3672919108 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag YmnX network-id 2"}}
05:58:05.783 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2867253757 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag YmnX network-id 1"}}
05:58:05.816 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3672919108 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag YmnX network-id 2"}}
05:58:05.851 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2867253757 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag YmnX network-id 1"}}
05:58:05.884 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3672919108 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag YmnX network-id 2"}}
05:58:19.750 [37mStreamer Component ->[34m {"type":"ping","time":1753826299}
05:59:19.711 [37mStreamer Component ->[34m {"type":"ping","time":1753826359}
06:00:19.711 [37mStreamer Component ->[34m {"type":"ping","time":1753826419}
06:01:19.716 [37mStreamer Component ->[34m {"type":"ping","time":1753826479}
06:02:19.720 [37mStreamer Component ->[34m {"type":"ping","time":1753826539}
06:03:19.714 [37mStreamer Component ->[34m {"type":"ping","time":1753826599}
06:04:19.704 [37mStreamer Component ->[34m {"type":"ping","time":1753826659}
06:05:19.700 [37mStreamer Component ->[34m {"type":"ping","time":1753826719}
06:06:19.731 [37mStreamer Component ->[34m {"type":"ping","time":1753826779}
06:07:19.697 [37mStreamer Component ->[34m {"type":"ping","time":1753826839}
06:08:19.694 [37mStreamer Component ->[34m {"type":"ping","time":1753826899}
06:09:19.728 [37mStreamer Component ->[34m {"type":"ping","time":1753826959}
06:10:19.729 [37mStreamer Component ->[34m {"type":"ping","time":1753827019}
06:11:19.726 [37mStreamer Component ->[34m {"type":"ping","time":1753827079}
06:12:19.720 [37mStreamer Component ->[34m {"type":"ping","time":1753827139}
06:13:19.751 [37mStreamer Component ->[34m {"type":"ping","time":1753827199}
06:14:19.711 [37mStreamer Component ->[34m {"type":"ping","time":1753827259}
06:15:19.707 [37mStreamer Component ->[34m {"type":"ping","time":1753827319}
06:16:19.709 [37mStreamer Component ->[34m {"type":"ping","time":1753827379}
06:17:19.735 [37mStreamer Component ->[34m {"type":"ping","time":1753827439}
06:18:19.706 [37mStreamer Component ->[34m {"type":"ping","time":1753827499}
06:19:19.736 [37mStreamer Component ->[34m {"type":"ping","time":1753827559}
06:20:19.722 [37mStreamer Component ->[34m {"type":"ping","time":1753827619}
06:21:19.718 [37mStreamer Component ->[34m {"type":"ping","time":1753827679}
06:22:21.279 streamer Streamer Component disconnected: 1006 - 
06:22:21.280 unsubscribing all players on Streamer Component
06:22:21.281 player 8 connection closed: 1005 - 
06:22:21.281 [37m[players] <-[32m {"type":"playerCount","count":1}
06:58:39.495 player 6 connection closed: 1001 - 
06:58:39.496 [37m[players] <-[32m {"type":"playerCount","count":0}
08:09:50.034 player 9 (::1) connected
08:09:50.035 [37m9 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:09:50.036 [37m[players] <-[32m {"type":"playerCount","count":1}
08:09:50.037 [37m9 ->[34m {"type":"listStreamers"}
08:09:50.037 [37m9 <-[32m {"type":"streamerList","ids":[]}
08:09:51.866 player 9 connection closed: 1001 - 
08:09:51.867 [37m[players] <-[32m {"type":"playerCount","count":0}
08:09:52.470 player 10 (::1) connected
08:09:52.471 [37m10 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:09:52.471 [37m[players] <-[32m {"type":"playerCount","count":1}
08:09:52.473 [37m10 ->[34m {"type":"listStreamers"}
08:09:52.473 [37m10 <-[32m {"type":"streamerList","ids":[]}
08:09:53.830 player 10 connection closed: 1005 - 
08:09:53.831 [37m[players] <-[32m {"type":"playerCount","count":0}
08:09:56.848 player 11 (::1) connected
08:09:56.849 [37m11 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:09:56.849 [37m[players] <-[32m {"type":"playerCount","count":1}
08:09:56.849 [37m11 ->[34m {"type":"listStreamers"}
08:09:56.850 [37m11 <-[32m {"type":"streamerList","ids":[]}
08:10:53.673 Streamer connected: ::1
08:10:53.674 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:10:53.674 [37m::1 <-[32m {"type":"identify"}
08:10:54.177 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
08:10:54.178 Registered new streamer: Streamer Component
08:11:12.351 player 11 connection closed: 1005 - 
08:11:12.352 [37m[players] <-[32m {"type":"playerCount","count":0}
08:11:14.959 player 12 (::1) connected
08:11:14.960 [37m12 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:11:14.960 [37m[players] <-[32m {"type":"playerCount","count":1}
08:11:14.961 [37m12 ->[34m {"type":"listStreamers"}
08:11:14.961 [37m12 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:11:14.989 [37m12 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:11:14.990 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"12","dataChannel":true,"sfu":false,"sendOffer":true}
08:11:15.084 [37mStreamer Component -> 12[36m {"type":"offer","playerId":12,"sdp":"v=0\r\no=- 239698093299418251 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:G7gr\r\na=ice-pwd:p9Kzubwf1pupxdGAd1wK/8gd\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:84:8A:74:84:D6:65:6A:2E:BE:02:7F:5B:F5:05:07:7E:59:A0:2D:5E:2C:7D:66:E9:5B:18:FC:C5:99:C9:FD\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1511657007 268271419\r\na=ssrc:1511657007 cname:YB16IDn/aGXSvuhW\r\na=ssrc:1511657007 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:268271419 cname:YB16IDn/aGXSvuhW\r\na=ssrc:268271419 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:G7gr\r\na=ice-pwd:p9Kzubwf1pupxdGAd1wK/8gd\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:84:8A:74:84:D6:65:6A:2E:BE:02:7F:5B:F5:05:07:7E:59:A0:2D:5E:2C:7D:66:E9:5B:18:FC:C5:99:C9:FD\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3781228564 cname:YB16IDn/aGXSvuhW\r\na=ssrc:3781228564 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:G7gr\r\na=ice-pwd:p9Kzubwf1pupxdGAd1wK/8gd\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:84:8A:74:84:D6:65:6A:2E:BE:02:7F:5B:F5:05:07:7E:59:A0:2D:5E:2C:7D:66:E9:5B:18:FC:C5:99:C9:FD\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:11:15.114 [37m12 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7262157236652362900 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:N9jd\r\na=ice-pwd:7Pr+eIIbnXdTu6D9T2E5guad\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C6:38:B8:D2:8D:A9:83:30:0B:A9:31:EB:3F:B5:E9:54:C6:3D:FD:33:FD:87:D0:F2:40:3F:7E:2B:31:85:8F:C9\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:N9jd\r\na=ice-pwd:7Pr+eIIbnXdTu6D9T2E5guad\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C6:38:B8:D2:8D:A9:83:30:0B:A9:31:EB:3F:B5:E9:54:C6:3D:FD:33:FD:87:D0:F2:40:3F:7E:2B:31:85:8F:C9\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- e0d93863-08d2-42f2-849b-cafa8f9119d0\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3580129623 cname:0LhDLB9HX10O8hyL\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:N9jd\r\na=ice-pwd:7Pr+eIIbnXdTu6D9T2E5guad\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C6:38:B8:D2:8D:A9:83:30:0B:A9:31:EB:3F:B5:E9:54:C6:3D:FD:33:FD:87:D0:F2:40:3F:7E:2B:31:85:8F:C9\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:11:15.114 [37m12 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2092393922 1 udp ********** *********** 56398 typ host generation 0 ufrag N9jd network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"N9jd"}}
08:11:15.115 [37m12 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2310369674 1 udp ********** ************* 56399 typ host generation 0 ufrag N9jd network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"N9jd"}}
08:11:15.117 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4243290517 1 udp ********** *********** 49152 typ host generation 0 ufrag G7gr network-id 1"}}
08:11:15.151 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2625785744 1 udp ********** ************* 49152 typ host generation 0 ufrag G7gr network-id 2"}}
08:11:15.185 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4243290517 1 udp ********** *********** 49153 typ host generation 0 ufrag G7gr network-id 1"}}
08:11:15.219 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2625785744 1 udp ********** ************* 49153 typ host generation 0 ufrag G7gr network-id 2"}}
08:11:15.253 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4243290517 1 udp ********** *********** 49154 typ host generation 0 ufrag G7gr network-id 1"}}
08:11:15.287 [37mStreamer Component -> 12[36m {"type":"iceCandidate","playerId":12,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2625785744 1 udp ********** ************* 49154 typ host generation 0 ufrag G7gr network-id 2"}}
08:11:54.243 [37mStreamer Component ->[34m {"type":"ping","time":1753834314}
08:12:22.946 player 12 connection closed: 1001 - 
08:12:22.947 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"12"}
08:12:22.951 [37m[players] <-[32m {"type":"playerCount","count":0}
08:12:23.642 player 13 (::1) connected
08:12:23.642 [37m13 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:12:23.643 [37m[players] <-[32m {"type":"playerCount","count":1}
08:12:23.644 [37m13 ->[34m {"type":"listStreamers"}
08:12:23.644 [37m13 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:12:23.671 [37m13 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:12:23.671 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"13","dataChannel":true,"sfu":false,"sendOffer":true}
08:12:23.749 [37mStreamer Component -> 13[36m {"type":"offer","playerId":13,"sdp":"v=0\r\no=- 4114650896804736570 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ziK0\r\na=ice-pwd:VPofGyQoeAnEO8ylEGzZPK4U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 13:92:33:F4:5E:46:B4:44:3B:85:13:DB:57:4E:52:A2:0E:26:3A:86:3B:7F:35:C1:07:3A:7E:F3:FB:1F:F5:32\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 675402567 3329949628\r\na=ssrc:675402567 cname:tQUl67U+GXm0eED6\r\na=ssrc:675402567 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3329949628 cname:tQUl67U+GXm0eED6\r\na=ssrc:3329949628 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ziK0\r\na=ice-pwd:VPofGyQoeAnEO8ylEGzZPK4U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 13:92:33:F4:5E:46:B4:44:3B:85:13:DB:57:4E:52:A2:0E:26:3A:86:3B:7F:35:C1:07:3A:7E:F3:FB:1F:F5:32\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2201941527 cname:tQUl67U+GXm0eED6\r\na=ssrc:2201941527 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:ziK0\r\na=ice-pwd:VPofGyQoeAnEO8ylEGzZPK4U\r\na=ice-options:trickle\r\na=fingerprint:sha-256 13:92:33:F4:5E:46:B4:44:3B:85:13:DB:57:4E:52:A2:0E:26:3A:86:3B:7F:35:C1:07:3A:7E:F3:FB:1F:F5:32\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:12:23.777 [37m13 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2299186005390447608 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:yFge\r\na=ice-pwd:82DTawOMzH6xDsojpWD2taVg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B9:FA:CA:87:A7:48:E6:B7:4B:6A:B9:45:A5:7B:81:30:DB:7E:21:43:06:43:F6:58:A4:09:E2:EA:56:3F:1F:98\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:yFge\r\na=ice-pwd:82DTawOMzH6xDsojpWD2taVg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B9:FA:CA:87:A7:48:E6:B7:4B:6A:B9:45:A5:7B:81:30:DB:7E:21:43:06:43:F6:58:A4:09:E2:EA:56:3F:1F:98\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- b7c4b13c-26b2-445f-bf43-40db489a7da0\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1947964535 cname:3OF99+F7fyw2GfG1\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:yFge\r\na=ice-pwd:82DTawOMzH6xDsojpWD2taVg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B9:FA:CA:87:A7:48:E6:B7:4B:6A:B9:45:A5:7B:81:30:DB:7E:21:43:06:43:F6:58:A4:09:E2:EA:56:3F:1F:98\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:12:23.777 [37m13 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3162360652 1 udp ********** *********** 62782 typ host generation 0 ufrag yFge network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"yFge"}}
08:12:23.778 [37m13 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1233116932 1 udp ********** ************* 62783 typ host generation 0 ufrag yFge network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"yFge"}}
08:12:23.783 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1973662653 1 udp ********** *********** 49152 typ host generation 0 ufrag ziK0 network-id 1"}}
08:12:23.817 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:365594040 1 udp ********** ************* 49152 typ host generation 0 ufrag ziK0 network-id 2"}}
08:12:23.851 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1973662653 1 udp ********** *********** 49153 typ host generation 0 ufrag ziK0 network-id 1"}}
08:12:23.885 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:365594040 1 udp ********** ************* 49153 typ host generation 0 ufrag ziK0 network-id 2"}}
08:12:23.919 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1973662653 1 udp ********** *********** 49154 typ host generation 0 ufrag ziK0 network-id 1"}}
08:12:23.953 [37mStreamer Component -> 13[36m {"type":"iceCandidate","playerId":13,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:365594040 1 udp ********** ************* 49154 typ host generation 0 ufrag ziK0 network-id 2"}}
08:12:54.240 [37mStreamer Component ->[34m {"type":"ping","time":1753834374}
08:13:54.240 [37mStreamer Component ->[34m {"type":"ping","time":1753834434}
08:14:54.208 [37mStreamer Component ->[34m {"type":"ping","time":1753834494}
08:15:54.243 [37mStreamer Component ->[34m {"type":"ping","time":1753834554}
08:16:54.245 [37mStreamer Component ->[34m {"type":"ping","time":1753834614}
08:17:54.242 [37mStreamer Component ->[34m {"type":"ping","time":1753834674}
08:18:28.903 player 13 connection closed: 1001 - 
08:18:28.903 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"13"}
08:18:28.909 [37m[players] <-[32m {"type":"playerCount","count":0}
08:18:30.278 player 14 (::1) connected
08:18:30.279 [37m14 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:18:30.279 [37m[players] <-[32m {"type":"playerCount","count":1}
08:18:30.279 [37m14 ->[34m {"type":"listStreamers"}
08:18:30.280 [37m14 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:18:30.303 [37m14 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:18:30.304 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"14","dataChannel":true,"sfu":false,"sendOffer":true}
08:18:30.375 [37mStreamer Component -> 14[36m {"type":"offer","playerId":14,"sdp":"v=0\r\no=- 2732747346690483101 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qYr4\r\na=ice-pwd:XIPxSCxWJV17QuT7Bkse7bam\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:FE:92:FF:A9:C6:FA:40:11:B0:25:F0:69:EB:A5:86:F5:5E:CA:2A:BF:D8:D8:11:83:19:56:84:6E:90:E4:39\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 687234516 2134129141\r\na=ssrc:687234516 cname:lJAzgEUyOJqr3GCG\r\na=ssrc:687234516 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2134129141 cname:lJAzgEUyOJqr3GCG\r\na=ssrc:2134129141 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qYr4\r\na=ice-pwd:XIPxSCxWJV17QuT7Bkse7bam\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:FE:92:FF:A9:C6:FA:40:11:B0:25:F0:69:EB:A5:86:F5:5E:CA:2A:BF:D8:D8:11:83:19:56:84:6E:90:E4:39\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2197999794 cname:lJAzgEUyOJqr3GCG\r\na=ssrc:2197999794 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:qYr4\r\na=ice-pwd:XIPxSCxWJV17QuT7Bkse7bam\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:FE:92:FF:A9:C6:FA:40:11:B0:25:F0:69:EB:A5:86:F5:5E:CA:2A:BF:D8:D8:11:83:19:56:84:6E:90:E4:39\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:18:30.407 [37m14 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6175286202072199457 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RGpV\r\na=ice-pwd:uGGCSYz3npiPs0AWy3yzeUyX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:82:38:39:30:A3:78:4B:09:F2:B3:61:48:12:4B:8C:94:D0:D7:A5:CA:E0:44:8E:53:26:17:20:F2:E7:C7:43\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RGpV\r\na=ice-pwd:uGGCSYz3npiPs0AWy3yzeUyX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:82:38:39:30:A3:78:4B:09:F2:B3:61:48:12:4B:8C:94:D0:D7:A5:CA:E0:44:8E:53:26:17:20:F2:E7:C7:43\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- a9d2917b-0bad-4acd-a90a-c13838189e0f\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:912866709 cname:ZLHNibjJxITZKM5p\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:RGpV\r\na=ice-pwd:uGGCSYz3npiPs0AWy3yzeUyX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FD:82:38:39:30:A3:78:4B:09:F2:B3:61:48:12:4B:8C:94:D0:D7:A5:CA:E0:44:8E:53:26:17:20:F2:E7:C7:43\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:18:30.408 [37m14 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:********** 1 udp ********** *********** 63175 typ host generation 0 ufrag RGpV network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RGpV"}}
08:18:30.409 [37m14 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:617134606 1 udp ********** ************* 63176 typ host generation 0 ufrag RGpV network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RGpV"}}
08:18:30.409 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:********** 1 udp ********** *********** 49152 typ host generation 0 ufrag qYr4 network-id 1"}}
08:18:30.441 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:564231051 1 udp ********** ************* 49152 typ host generation 0 ufrag qYr4 network-id 2"}}
08:18:30.475 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:********** 1 udp ********** *********** 49153 typ host generation 0 ufrag qYr4 network-id 1"}}
08:18:30.509 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:564231051 1 udp ********** ************* 49153 typ host generation 0 ufrag qYr4 network-id 2"}}
08:18:30.544 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:********** 1 udp ********** *********** 49154 typ host generation 0 ufrag qYr4 network-id 1"}}
08:18:30.577 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:564231051 1 udp ********** ************* 49154 typ host generation 0 ufrag qYr4 network-id 2"}}
08:18:30.611 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2936091302 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag qYr4 network-id 1"}}
08:18:30.644 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3742084895 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag qYr4 network-id 2"}}
08:18:30.678 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2936091302 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag qYr4 network-id 1"}}
08:18:30.712 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3742084895 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag qYr4 network-id 2"}}
08:18:30.745 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2936091302 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag qYr4 network-id 1"}}
08:18:30.779 [37mStreamer Component -> 14[36m {"type":"iceCandidate","playerId":14,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3742084895 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag qYr4 network-id 2"}}
08:18:54.242 [37mStreamer Component ->[34m {"type":"ping","time":1753834734}
08:19:54.235 [37mStreamer Component ->[34m {"type":"ping","time":1753834794}
08:20:54.247 [37mStreamer Component ->[34m {"type":"ping","time":1753834854}
08:21:54.239 [37mStreamer Component ->[34m {"type":"ping","time":1753834914}
08:22:54.205 [37mStreamer Component ->[34m {"type":"ping","time":1753834974}
08:23:54.251 [37mStreamer Component ->[34m {"type":"ping","time":1753835034}
08:24:54.245 [37mStreamer Component ->[34m {"type":"ping","time":1753835094}
08:25:54.259 [37mStreamer Component ->[34m {"type":"ping","time":1753835154}
08:26:54.238 [37mStreamer Component ->[34m {"type":"ping","time":1753835214}
08:27:54.259 [37mStreamer Component ->[34m {"type":"ping","time":1753835274}
08:28:43.602 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
08:28:43.603 unsubscribing all players on Streamer Component
08:28:43.604 player 14 connection closed: 1005 - 
08:28:43.604 [37m[players] <-[32m {"type":"playerCount","count":0}
08:29:03.828 Streamer connected: ::1
08:29:03.829 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:29:03.829 [37m::1 <-[32m {"type":"identify"}
08:29:04.292 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
08:29:04.293 Registered new streamer: Streamer Component
08:29:29.528 player 15 (::1) connected
08:29:29.529 [37m15 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:29:29.529 [37m[players] <-[32m {"type":"playerCount","count":1}
08:29:29.530 [37m15 ->[34m {"type":"listStreamers"}
08:29:29.531 [37m15 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:29:29.562 [37m15 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:29:29.563 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"15","dataChannel":true,"sfu":false,"sendOffer":true}
08:29:29.683 [37mStreamer Component -> 15[36m {"type":"offer","playerId":15,"sdp":"v=0\r\no=- 556157777375269050 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QOji\r\na=ice-pwd:ZSpBbZfxkSq5eayJG8rLEGlM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6D:D9:9D:4D:71:85:F5:A2:F0:B9:63:D6:93:48:23:FD:CC:8E:B2:24:90:F2:0A:52:3D:8B:C6:CD:F6:9D:F4:4D\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2569563975 3941291556\r\na=ssrc:2569563975 cname:sOahHceqB1+PaAWT\r\na=ssrc:2569563975 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3941291556 cname:sOahHceqB1+PaAWT\r\na=ssrc:3941291556 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QOji\r\na=ice-pwd:ZSpBbZfxkSq5eayJG8rLEGlM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6D:D9:9D:4D:71:85:F5:A2:F0:B9:63:D6:93:48:23:FD:CC:8E:B2:24:90:F2:0A:52:3D:8B:C6:CD:F6:9D:F4:4D\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1509931024 cname:sOahHceqB1+PaAWT\r\na=ssrc:1509931024 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:QOji\r\na=ice-pwd:ZSpBbZfxkSq5eayJG8rLEGlM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6D:D9:9D:4D:71:85:F5:A2:F0:B9:63:D6:93:48:23:FD:CC:8E:B2:24:90:F2:0A:52:3D:8B:C6:CD:F6:9D:F4:4D\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:29:29.685 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3732742177 1 udp ********** *********** 49152 typ host generation 0 ufrag QOji network-id 1"}}
08:29:29.717 [37m15 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 1796273069656492385 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:hQTY\r\na=ice-pwd:3s1zdLbBLWbIft+ozXIHOOl6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 33:37:D3:94:98:9A:A4:11:82:00:95:D0:C1:6F:45:73:D1:85:F7:98:C9:78:C4:AF:7F:D4:A9:4F:0B:87:1D:2A\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:hQTY\r\na=ice-pwd:3s1zdLbBLWbIft+ozXIHOOl6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 33:37:D3:94:98:9A:A4:11:82:00:95:D0:C1:6F:45:73:D1:85:F7:98:C9:78:C4:AF:7F:D4:A9:4F:0B:87:1D:2A\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 59a2f90a-57fc-416f-a2d4-24c0cb34df31\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1388824790 cname:g+nXRTd0WCfonTPP\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:hQTY\r\na=ice-pwd:3s1zdLbBLWbIft+ozXIHOOl6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 33:37:D3:94:98:9A:A4:11:82:00:95:D0:C1:6F:45:73:D1:85:F7:98:C9:78:C4:AF:7F:D4:A9:4F:0B:87:1D:2A\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:29:29.719 [37m15 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:487014647 1 udp ********** *********** 58208 typ host generation 0 ufrag hQTY network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"hQTY"}}
08:29:29.719 [37m15 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3892663487 1 udp ********** ************* 58209 typ host generation 0 ufrag hQTY network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"hQTY"}}
08:29:29.720 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2927082904 1 udp ********** ************* 49152 typ host generation 0 ufrag QOji network-id 2"}}
08:29:29.751 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3732742177 1 udp ********** *********** 49153 typ host generation 0 ufrag QOji network-id 1"}}
08:29:29.784 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2927082904 1 udp ********** ************* 49153 typ host generation 0 ufrag QOji network-id 2"}}
08:29:29.818 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3732742177 1 udp ********** *********** 49154 typ host generation 0 ufrag QOji network-id 1"}}
08:29:29.851 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2927082904 1 udp ********** ************* 49154 typ host generation 0 ufrag QOji network-id 2"}}
08:29:29.884 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:551023797 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag QOji network-id 1"}}
08:29:29.918 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1356687628 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag QOji network-id 2"}}
08:29:29.952 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:551023797 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag QOji network-id 1"}}
08:29:29.986 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1356687628 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag QOji network-id 2"}}
08:29:30.019 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:551023797 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag QOji network-id 1"}}
08:29:30.053 [37mStreamer Component -> 15[36m {"type":"iceCandidate","playerId":15,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1356687628 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag QOji network-id 2"}}
08:30:04.374 [37mStreamer Component ->[34m {"type":"ping","time":1753835404}
08:31:04.720 [37mStreamer Component ->[34m {"type":"ping","time":1753835464}
08:32:05.975 [37mStreamer Component ->[34m {"type":"ping","time":1753835525}
08:33:05.987 [37mStreamer Component ->[34m {"type":"ping","time":1753835585}
08:34:06.008 [37mStreamer Component ->[34m {"type":"ping","time":1753835645}
08:34:29.006 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
08:34:29.006 unsubscribing all players on Streamer Component
08:34:29.007 player 15 connection closed: 1005 - 
08:34:29.007 [37m[players] <-[32m {"type":"playerCount","count":0}
08:35:42.382 Streamer connected: ::1
08:35:42.383 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:35:42.384 [37m::1 <-[32m {"type":"identify"}
08:35:42.881 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
08:35:42.881 Registered new streamer: Streamer Component
08:36:43.821 [37mStreamer Component ->[34m {"type":"ping","time":1753835803}
08:37:20.416 player 16 (::1) connected
08:37:20.416 [37m16 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:37:20.417 [37m[players] <-[32m {"type":"playerCount","count":1}
08:37:20.418 [37m16 ->[34m {"type":"listStreamers"}
08:37:20.419 [37m16 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:37:20.442 [37m16 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:37:20.443 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"16","dataChannel":true,"sfu":false,"sendOffer":true}
08:37:20.537 [37mStreamer Component -> 16[36m {"type":"offer","playerId":16,"sdp":"v=0\r\no=- 8795596028596004489 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Zslv\r\na=ice-pwd:/SGcBqEwpFHbMPA8TOCaeWgG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4D:9B:84:12:74:1E:D8:CB:78:DC:AB:1D:B6:60:DD:EB:86:7B:81:4F:E7:7B:CA:4A:E4:C2:96:6E:D9:8E:02:D8\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3420064201 88512673\r\na=ssrc:3420064201 cname:8CFlkFQw/VoLX8t5\r\na=ssrc:3420064201 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:88512673 cname:8CFlkFQw/VoLX8t5\r\na=ssrc:88512673 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Zslv\r\na=ice-pwd:/SGcBqEwpFHbMPA8TOCaeWgG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4D:9B:84:12:74:1E:D8:CB:78:DC:AB:1D:B6:60:DD:EB:86:7B:81:4F:E7:7B:CA:4A:E4:C2:96:6E:D9:8E:02:D8\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:103597019 cname:8CFlkFQw/VoLX8t5\r\na=ssrc:103597019 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Zslv\r\na=ice-pwd:/SGcBqEwpFHbMPA8TOCaeWgG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4D:9B:84:12:74:1E:D8:CB:78:DC:AB:1D:B6:60:DD:EB:86:7B:81:4F:E7:7B:CA:4A:E4:C2:96:6E:D9:8E:02:D8\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:37:20.539 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2165095882 1 udp ********** *********** 49152 typ host generation 0 ufrag Zslv network-id 1"}}
08:37:20.571 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3781528527 1 udp ********** ************* 49152 typ host generation 0 ufrag Zslv network-id 2"}}
08:37:20.587 [37m16 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 3585823515021620042 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:euQa\r\na=ice-pwd:qJ0IJ9viovm1F2r2kKrcoIRZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:11:14:CD:5E:D7:F1:16:43:2A:CC:50:98:6A:51:02:14:48:A5:16:BE:9E:3E:3A:83:44:D6:37:B2:A5:6D:1D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:euQa\r\na=ice-pwd:qJ0IJ9viovm1F2r2kKrcoIRZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:11:14:CD:5E:D7:F1:16:43:2A:CC:50:98:6A:51:02:14:48:A5:16:BE:9E:3E:3A:83:44:D6:37:B2:A5:6D:1D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 52987b2f-f9d4-434c-a8b1-8eef4e213c30\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:165586954 cname:7M/84a0DqUG5ZX4Q\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:euQa\r\na=ice-pwd:qJ0IJ9viovm1F2r2kKrcoIRZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:11:14:CD:5E:D7:F1:16:43:2A:CC:50:98:6A:51:02:14:48:A5:16:BE:9E:3E:3A:83:44:D6:37:B2:A5:6D:1D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:37:20.588 [37m16 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2706985338 1 udp ********** *********** 63961 typ host generation 0 ufrag euQa network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"euQa"}}
08:37:20.588 [37m16 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1415267634 1 udp ********** ************* 63962 typ host generation 0 ufrag euQa network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"euQa"}}
08:37:20.605 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2165095882 1 udp ********** *********** 49153 typ host generation 0 ufrag Zslv network-id 1"}}
08:37:20.638 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3781528527 1 udp ********** ************* 49153 typ host generation 0 ufrag Zslv network-id 2"}}
08:37:20.672 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2165095882 1 udp ********** *********** 49154 typ host generation 0 ufrag Zslv network-id 1"}}
08:37:20.706 [37mStreamer Component -> 16[36m {"type":"iceCandidate","playerId":16,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3781528527 1 udp ********** ************* 49154 typ host generation 0 ufrag Zslv network-id 2"}}
08:37:44.229 [37mStreamer Component ->[34m {"type":"ping","time":1753835864}
08:38:44.216 [37mStreamer Component ->[34m {"type":"ping","time":1753835924}
08:39:44.216 [37mStreamer Component ->[34m {"type":"ping","time":1753835984}
08:40:44.206 [37mStreamer Component ->[34m {"type":"ping","time":1753836044}
08:41:44.239 [37mStreamer Component ->[34m {"type":"ping","time":1753836104}
08:42:44.190 [37mStreamer Component ->[34m {"type":"ping","time":1753836164}
08:43:44.245 [37mStreamer Component ->[34m {"type":"ping","time":1753836224}
08:44:44.198 [37mStreamer Component ->[34m {"type":"ping","time":1753836284}
08:45:44.207 [37mStreamer Component ->[34m {"type":"ping","time":1753836344}
08:46:07.779 player 16 connection closed: 1001 - 
08:46:07.784 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"16"}
08:46:07.785 [37m[players] <-[32m {"type":"playerCount","count":0}
08:46:09.567 player 17 (::1) connected
08:46:09.568 [37m17 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:46:09.568 [37m[players] <-[32m {"type":"playerCount","count":1}
08:46:09.568 [37m17 ->[34m {"type":"listStreamers"}
08:46:09.569 [37m17 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:46:09.596 [37m17 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:46:09.597 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"17","dataChannel":true,"sfu":false,"sendOffer":true}
08:46:09.663 [37mStreamer Component -> 17[36m {"type":"offer","playerId":17,"sdp":"v=0\r\no=- 7989714551028034974 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Vr5E\r\na=ice-pwd:CHtEZEcCYM0D7/UKXMmRZpcA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 64:3C:5B:57:98:09:96:B6:1B:49:F2:31:FC:66:F3:89:FB:26:9B:D2:28:60:21:A5:AA:B1:1D:AE:07:27:0F:57\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2375890930 2195191006\r\na=ssrc:2375890930 cname:3I8VkFV6Cg4TwHhV\r\na=ssrc:2375890930 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2195191006 cname:3I8VkFV6Cg4TwHhV\r\na=ssrc:2195191006 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Vr5E\r\na=ice-pwd:CHtEZEcCYM0D7/UKXMmRZpcA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 64:3C:5B:57:98:09:96:B6:1B:49:F2:31:FC:66:F3:89:FB:26:9B:D2:28:60:21:A5:AA:B1:1D:AE:07:27:0F:57\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2497605379 cname:3I8VkFV6Cg4TwHhV\r\na=ssrc:2497605379 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Vr5E\r\na=ice-pwd:CHtEZEcCYM0D7/UKXMmRZpcA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 64:3C:5B:57:98:09:96:B6:1B:49:F2:31:FC:66:F3:89:FB:26:9B:D2:28:60:21:A5:AA:B1:1D:AE:07:27:0F:57\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:46:09.696 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:474108130 1 udp ********** *********** 49152 typ host generation 0 ufrag Vr5E network-id 1"}}
08:46:09.701 [37m17 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2032541602797547480 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EGEk\r\na=ice-pwd:z0jYGJk9NPNKGPrCmPonDxFb\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:49:43:26:77:DC:D9:A4:9E:F5:9C:62:AC:10:92:9A:4C:CA:4F:DB:2A:02:DF:FB:4D:00:63:C9:78:7B:95:B3\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EGEk\r\na=ice-pwd:z0jYGJk9NPNKGPrCmPonDxFb\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:49:43:26:77:DC:D9:A4:9E:F5:9C:62:AC:10:92:9A:4C:CA:4F:DB:2A:02:DF:FB:4D:00:63:C9:78:7B:95:B3\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- eb605196-38cb-4bad-a332-7af91ddec82e\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4245403817 cname:+osLookfDcH5CfgD\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:EGEk\r\na=ice-pwd:z0jYGJk9NPNKGPrCmPonDxFb\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AD:49:43:26:77:DC:D9:A4:9E:F5:9C:62:AC:10:92:9A:4C:CA:4F:DB:2A:02:DF:FB:4D:00:63:C9:78:7B:95:B3\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:46:09.702 [37m17 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2425089516 1 udp ********** *********** 56915 typ host generation 0 ufrag EGEk network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"EGEk"}}
08:46:09.705 [37m17 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3900253786 1 udp ********** ************* 56916 typ host generation 0 ufrag EGEk network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"EGEk"}}
08:46:09.730 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1816710491 1 udp ********** ************* 49152 typ host generation 0 ufrag Vr5E network-id 2"}}
08:46:09.763 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:474108130 1 udp ********** *********** 49153 typ host generation 0 ufrag Vr5E network-id 1"}}
08:46:09.797 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1816710491 1 udp ********** ************* 49153 typ host generation 0 ufrag Vr5E network-id 2"}}
08:46:09.831 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:474108130 1 udp ********** *********** 49154 typ host generation 0 ufrag Vr5E network-id 1"}}
08:46:09.865 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1816710491 1 udp ********** ************* 49154 typ host generation 0 ufrag Vr5E network-id 2"}}
08:46:09.898 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3806889078 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag Vr5E network-id 1"}}
08:46:09.932 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2464291279 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag Vr5E network-id 2"}}
08:46:09.966 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3806889078 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag Vr5E network-id 1"}}
08:46:09.999 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2464291279 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag Vr5E network-id 2"}}
08:46:10.033 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3806889078 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag Vr5E network-id 1"}}
08:46:10.066 [37mStreamer Component -> 17[36m {"type":"iceCandidate","playerId":17,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2464291279 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag Vr5E network-id 2"}}
08:46:44.197 [37mStreamer Component ->[34m {"type":"ping","time":1753836404}
08:47:44.228 [37mStreamer Component ->[34m {"type":"ping","time":1753836464}
08:48:44.206 [37mStreamer Component ->[34m {"type":"ping","time":1753836524}
08:49:44.234 [37mStreamer Component ->[34m {"type":"ping","time":1753836584}
08:50:44.238 [37mStreamer Component ->[34m {"type":"ping","time":1753836644}
08:51:44.216 [37mStreamer Component ->[34m {"type":"ping","time":1753836704}
08:52:44.193 [37mStreamer Component ->[34m {"type":"ping","time":1753836764}
08:53:44.236 [37mStreamer Component ->[34m {"type":"ping","time":1753836824}
08:53:47.335 player 17 connection closed: 1001 - 
08:53:47.339 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"17"}
08:53:47.339 [37m[players] <-[32m {"type":"playerCount","count":0}
08:53:48.040 player 18 (::1) connected
08:53:48.041 [37m18 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:53:48.041 [37m[players] <-[32m {"type":"playerCount","count":1}
08:53:48.042 [37m18 ->[34m {"type":"listStreamers"}
08:53:48.042 [37m18 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:53:48.068 [37m18 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:53:48.069 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"18","dataChannel":true,"sfu":false,"sendOffer":true}
08:53:48.156 [37mStreamer Component -> 18[36m {"type":"offer","playerId":18,"sdp":"v=0\r\no=- 750190418775799807 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:3I+k\r\na=ice-pwd:B87OgKgjeKeqxtNCAhq1ZJ2X\r\na=ice-options:trickle\r\na=fingerprint:sha-256 14:1E:DD:CC:82:D6:7D:25:C2:02:AF:BA:9D:64:B5:08:A8:C8:63:7D:EE:83:3B:08:80:3D:E1:4F:D3:A8:A8:E6\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3281311715 2685054766\r\na=ssrc:3281311715 cname:dkO1FG6abvwTAVri\r\na=ssrc:3281311715 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2685054766 cname:dkO1FG6abvwTAVri\r\na=ssrc:2685054766 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:3I+k\r\na=ice-pwd:B87OgKgjeKeqxtNCAhq1ZJ2X\r\na=ice-options:trickle\r\na=fingerprint:sha-256 14:1E:DD:CC:82:D6:7D:25:C2:02:AF:BA:9D:64:B5:08:A8:C8:63:7D:EE:83:3B:08:80:3D:E1:4F:D3:A8:A8:E6\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4238744616 cname:dkO1FG6abvwTAVri\r\na=ssrc:4238744616 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:3I+k\r\na=ice-pwd:B87OgKgjeKeqxtNCAhq1ZJ2X\r\na=ice-options:trickle\r\na=fingerprint:sha-256 14:1E:DD:CC:82:D6:7D:25:C2:02:AF:BA:9D:64:B5:08:A8:C8:63:7D:EE:83:3B:08:80:3D:E1:4F:D3:A8:A8:E6\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:53:48.189 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2575325918 1 udp ********** *********** 49152 typ host generation 0 ufrag 3I+k network-id 1"}}
08:53:48.196 [37m18 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8411055627640623070 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zf8x\r\na=ice-pwd:Ih3hO/HWI8bDWXA5GEbdbvsN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EC:A5:5B:33:E2:8B:A0:43:FF:C0:15:D8:60:6D:1B:B7:01:6C:23:28:FD:2D:98:91:DC:BC:74:AB:D9:E9:E9:FF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zf8x\r\na=ice-pwd:Ih3hO/HWI8bDWXA5GEbdbvsN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EC:A5:5B:33:E2:8B:A0:43:FF:C0:15:D8:60:6D:1B:B7:01:6C:23:28:FD:2D:98:91:DC:BC:74:AB:D9:E9:E9:FF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- bb8982e9-840a-4143-94fb-eb44f8be3dfb\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3591950316 cname:khSRs5i49jlVgU0k\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:zf8x\r\na=ice-pwd:Ih3hO/HWI8bDWXA5GEbdbvsN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EC:A5:5B:33:E2:8B:A0:43:FF:C0:15:D8:60:6D:1B:B7:01:6C:23:28:FD:2D:98:91:DC:BC:74:AB:D9:E9:E9:FF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:53:48.197 [37m18 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:42048101 1 udp ********** *********** 58614 typ host generation 0 ufrag zf8x network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"zf8x"}}
08:53:48.197 [37m18 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2054377939 1 udp ********** ************* 58615 typ host generation 0 ufrag zf8x network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"zf8x"}}
08:53:48.223 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4192824539 1 udp ********** ************* 49152 typ host generation 0 ufrag 3I+k network-id 2"}}
08:53:48.257 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2575325918 1 udp ********** *********** 49153 typ host generation 0 ufrag 3I+k network-id 1"}}
08:53:48.291 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4192824539 1 udp ********** ************* 49153 typ host generation 0 ufrag 3I+k network-id 2"}}
08:53:48.324 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2575325918 1 udp ********** *********** 49154 typ host generation 0 ufrag 3I+k network-id 1"}}
08:53:48.358 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4192824539 1 udp ********** ************* 49154 typ host generation 0 ufrag 3I+k network-id 2"}}
08:53:48.392 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3880761414 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag 3I+k network-id 1"}}
08:53:48.426 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2267450947 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag 3I+k network-id 2"}}
08:53:48.459 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3880761414 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag 3I+k network-id 1"}}
08:53:48.493 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2267450947 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag 3I+k network-id 2"}}
08:53:48.527 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3880761414 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag 3I+k network-id 1"}}
08:53:48.560 [37mStreamer Component -> 18[36m {"type":"iceCandidate","playerId":18,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2267450947 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag 3I+k network-id 2"}}
08:54:44.199 [37mStreamer Component ->[34m {"type":"ping","time":1753836884}
08:55:44.182 [37mStreamer Component ->[34m {"type":"ping","time":1753836944}
08:56:44.230 [37mStreamer Component ->[34m {"type":"ping","time":1753837004}
08:57:44.205 [37mStreamer Component ->[34m {"type":"ping","time":1753837064}
08:58:23.827 player 18 connection closed: 1001 - 
08:58:23.831 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"18"}
08:58:23.832 [37m[players] <-[32m {"type":"playerCount","count":0}
08:58:26.358 player 19 (::1) connected
08:58:26.359 [37m19 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
08:58:26.359 [37m[players] <-[32m {"type":"playerCount","count":1}
08:58:26.359 [37m19 ->[34m {"type":"listStreamers"}
08:58:26.360 [37m19 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
08:58:26.390 [37m19 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
08:58:26.390 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"19","dataChannel":true,"sfu":false,"sendOffer":true}
08:58:26.480 [37mStreamer Component -> 19[36m {"type":"offer","playerId":19,"sdp":"v=0\r\no=- 6109314605964559024 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uJjO\r\na=ice-pwd:8YoNmca9Y+Z0ipOkGrpv4fHD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7C:FD:74:C1:75:09:84:2D:A2:20:5E:0F:E1:2D:30:19:1D:15:60:D9:47:1F:6D:60:A1:9F:CB:F7:21:DD:9F:53\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1723431585 1351019212\r\na=ssrc:1723431585 cname:l6Z6YoVAJL2ivkq1\r\na=ssrc:1723431585 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1351019212 cname:l6Z6YoVAJL2ivkq1\r\na=ssrc:1351019212 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uJjO\r\na=ice-pwd:8YoNmca9Y+Z0ipOkGrpv4fHD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7C:FD:74:C1:75:09:84:2D:A2:20:5E:0F:E1:2D:30:19:1D:15:60:D9:47:1F:6D:60:A1:9F:CB:F7:21:DD:9F:53\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1505925460 cname:l6Z6YoVAJL2ivkq1\r\na=ssrc:1505925460 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uJjO\r\na=ice-pwd:8YoNmca9Y+Z0ipOkGrpv4fHD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7C:FD:74:C1:75:09:84:2D:A2:20:5E:0F:E1:2D:30:19:1D:15:60:D9:47:1F:6D:60:A1:9F:CB:F7:21:DD:9F:53\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:58:26.513 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:32000120 1 udp ********** *********** 49152 typ host generation 0 ufrag uJjO network-id 1"}}
08:58:26.518 [37m19 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7852748669824859222 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:I2yp\r\na=ice-pwd:xxcjVLD0QmMRGDv1+W7Cirec\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FC:DC:E7:97:3D:FB:0B:2F:65:D8:1C:70:9B:E3:04:AE:D8:07:5C:BB:99:AB:45:A7:C5:D1:E3:14:9C:80:30:9C\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:I2yp\r\na=ice-pwd:xxcjVLD0QmMRGDv1+W7Cirec\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FC:DC:E7:97:3D:FB:0B:2F:65:D8:1C:70:9B:E3:04:AE:D8:07:5C:BB:99:AB:45:A7:C5:D1:E3:14:9C:80:30:9C\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f8fac28e-b88d-4e9e-bdc2-b96e80f20a2b\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2947843420 cname:dSZRXPZeIwdKIpTc\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:I2yp\r\na=ice-pwd:xxcjVLD0QmMRGDv1+W7Cirec\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FC:DC:E7:97:3D:FB:0B:2F:65:D8:1C:70:9B:E3:04:AE:D8:07:5C:BB:99:AB:45:A7:C5:D1:E3:14:9C:80:30:9C\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
08:58:26.519 [37m19 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2547693185 1 udp ********** *********** 59447 typ host generation 0 ufrag I2yp network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"I2yp"}}
08:58:26.520 [37m19 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2584390102 1 udp ********** ************* 59448 typ host generation 0 ufrag I2yp network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"I2yp"}}
08:58:26.547 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1635876477 1 udp ********** ************* 49152 typ host generation 0 ufrag uJjO network-id 2"}}
08:58:26.581 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:32000120 1 udp ********** *********** 49153 typ host generation 0 ufrag uJjO network-id 1"}}
08:58:26.615 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1635876477 1 udp ********** ************* 49153 typ host generation 0 ufrag uJjO network-id 2"}}
08:58:26.649 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:32000120 1 udp ********** *********** 49154 typ host generation 0 ufrag uJjO network-id 1"}}
08:58:26.682 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1635876477 1 udp ********** ************* 49154 typ host generation 0 ufrag uJjO network-id 2"}}
08:58:26.716 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2133306080 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag uJjO network-id 1"}}
08:58:26.750 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:525245669 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag uJjO network-id 2"}}
08:58:26.783 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2133306080 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag uJjO network-id 1"}}
08:58:26.817 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:525245669 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag uJjO network-id 2"}}
08:58:26.851 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2133306080 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag uJjO network-id 1"}}
08:58:26.884 [37mStreamer Component -> 19[36m {"type":"iceCandidate","playerId":19,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:525245669 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag uJjO network-id 2"}}
08:58:44.188 [37mStreamer Component ->[34m {"type":"ping","time":1753837124}
08:59:44.208 [37mStreamer Component ->[34m {"type":"ping","time":1753837184}
09:00:44.234 [37mStreamer Component ->[34m {"type":"ping","time":1753837244}
09:01:44.213 [37mStreamer Component ->[34m {"type":"ping","time":1753837304}
09:02:44.175 [37mStreamer Component ->[34m {"type":"ping","time":1753837364}
09:03:44.227 [37mStreamer Component ->[34m {"type":"ping","time":1753837424}
09:04:44.183 [37mStreamer Component ->[34m {"type":"ping","time":1753837484}
09:05:44.249 [37mStreamer Component ->[34m {"type":"ping","time":1753837544}
09:06:44.189 [37mStreamer Component ->[34m {"type":"ping","time":1753837604}
09:07:44.242 [37mStreamer Component ->[34m {"type":"ping","time":1753837664}
09:08:44.231 [37mStreamer Component ->[34m {"type":"ping","time":1753837724}
09:09:44.192 [37mStreamer Component ->[34m {"type":"ping","time":1753837784}
09:10:44.210 [37mStreamer Component ->[34m {"type":"ping","time":1753837844}
09:11:44.226 [37mStreamer Component ->[34m {"type":"ping","time":1753837904}
09:12:44.252 [37mStreamer Component ->[34m {"type":"ping","time":1753837964}
09:13:44.202 [37mStreamer Component ->[34m {"type":"ping","time":1753838024}
09:14:44.206 [37mStreamer Component ->[34m {"type":"ping","time":1753838084}
09:15:44.205 [37mStreamer Component ->[34m {"type":"ping","time":1753838144}
09:16:44.213 [37mStreamer Component ->[34m {"type":"ping","time":1753838204}
09:17:44.236 [37mStreamer Component ->[34m {"type":"ping","time":1753838264}
09:18:44.223 [37mStreamer Component ->[34m {"type":"ping","time":1753838324}
09:19:44.205 [37mStreamer Component ->[34m {"type":"ping","time":1753838384}
09:20:44.252 [37mStreamer Component ->[34m {"type":"ping","time":1753838444}
09:21:44.208 [37mStreamer Component ->[34m {"type":"ping","time":1753838504}
09:22:44.210 [37mStreamer Component ->[34m {"type":"ping","time":1753838564}
09:23:44.235 [37mStreamer Component ->[34m {"type":"ping","time":1753838624}
09:24:44.201 [37mStreamer Component ->[34m {"type":"ping","time":1753838684}
09:25:44.207 [37mStreamer Component ->[34m {"type":"ping","time":1753838744}
09:26:44.228 [37mStreamer Component ->[34m {"type":"ping","time":1753838804}
09:27:44.246 [37mStreamer Component ->[34m {"type":"ping","time":1753838864}
09:28:44.199 [37mStreamer Component ->[34m {"type":"ping","time":1753838924}
09:29:44.199 [37mStreamer Component ->[34m {"type":"ping","time":1753838984}
09:30:44.187 [37mStreamer Component ->[34m {"type":"ping","time":1753839044}
09:31:44.233 [37mStreamer Component ->[34m {"type":"ping","time":1753839104}
09:32:44.217 [37mStreamer Component ->[34m {"type":"ping","time":1753839164}
09:33:44.198 [37mStreamer Component ->[34m {"type":"ping","time":1753839224}
09:34:44.240 [37mStreamer Component ->[34m {"type":"ping","time":1753839284}
09:35:44.228 [37mStreamer Component ->[34m {"type":"ping","time":1753839344}
09:36:44.219 [37mStreamer Component ->[34m {"type":"ping","time":1753839404}
09:37:44.215 [37mStreamer Component ->[34m {"type":"ping","time":1753839464}
09:38:38.609 player 19 connection closed: 1001 - 
09:38:38.610 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"19"}
09:38:38.614 [37m[players] <-[32m {"type":"playerCount","count":0}
09:38:41.592 player 20 (::1) connected
09:38:41.592 [37m20 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:38:41.593 [37m[players] <-[32m {"type":"playerCount","count":1}
09:38:41.594 [37m20 ->[34m {"type":"listStreamers"}
09:38:41.594 [37m20 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:38:41.625 [37m20 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:38:41.625 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"20","dataChannel":true,"sfu":false,"sendOffer":true}
09:38:41.681 [37mStreamer Component -> 20[36m {"type":"offer","playerId":20,"sdp":"v=0\r\no=- 2424621917072714169 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:D+v6\r\na=ice-pwd:zdL5+ygh3v0g56H2RuJDROAp\r\na=ice-options:trickle\r\na=fingerprint:sha-256 90:33:48:49:3F:D3:86:5D:32:D8:35:CD:E2:8E:43:30:4F:91:AF:0F:3F:20:96:97:43:4C:89:E6:2B:F2:43:3C\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1424393092 3938036799\r\na=ssrc:1424393092 cname:OypgHukWZxR1PN15\r\na=ssrc:1424393092 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3938036799 cname:OypgHukWZxR1PN15\r\na=ssrc:3938036799 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:D+v6\r\na=ice-pwd:zdL5+ygh3v0g56H2RuJDROAp\r\na=ice-options:trickle\r\na=fingerprint:sha-256 90:33:48:49:3F:D3:86:5D:32:D8:35:CD:E2:8E:43:30:4F:91:AF:0F:3F:20:96:97:43:4C:89:E6:2B:F2:43:3C\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3599602397 cname:OypgHukWZxR1PN15\r\na=ssrc:3599602397 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:D+v6\r\na=ice-pwd:zdL5+ygh3v0g56H2RuJDROAp\r\na=ice-options:trickle\r\na=fingerprint:sha-256 90:33:48:49:3F:D3:86:5D:32:D8:35:CD:E2:8E:43:30:4F:91:AF:0F:3F:20:96:97:43:4C:89:E6:2B:F2:43:3C\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:41.714 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1156323576 1 udp ********** *********** 49152 typ host generation 0 ufrag D+v6 network-id 1"}}
09:38:41.732 [37m20 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 3079324780860640177 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:sGkv\r\na=ice-pwd:DFdHOQJPEH54QDmJDCtMI2Ey\r\na=ice-options:trickle\r\na=fingerprint:sha-256 21:E2:5E:7E:F1:3B:C7:86:DA:1A:7C:9D:35:8E:74:B0:99:58:7F:B5:10:9F:82:0F:89:F5:07:47:2B:31:CD:68\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:sGkv\r\na=ice-pwd:DFdHOQJPEH54QDmJDCtMI2Ey\r\na=ice-options:trickle\r\na=fingerprint:sha-256 21:E2:5E:7E:F1:3B:C7:86:DA:1A:7C:9D:35:8E:74:B0:99:58:7F:B5:10:9F:82:0F:89:F5:07:47:2B:31:CD:68\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 88240ccc-8467-4cd3-a1bd-7c53dc8fbbce\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:933420352 cname:jZyLe7pKXLIrrYI8\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:sGkv\r\na=ice-pwd:DFdHOQJPEH54QDmJDCtMI2Ey\r\na=ice-options:trickle\r\na=fingerprint:sha-256 21:E2:5E:7E:F1:3B:C7:86:DA:1A:7C:9D:35:8E:74:B0:99:58:7F:B5:10:9F:82:0F:89:F5:07:47:2B:31:CD:68\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:41.741 [37m20 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1006737513 1 udp ********** *********** 50895 typ host generation 0 ufrag sGkv network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"sGkv"}}
09:38:41.742 [37m20 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3372452897 1 udp ********** ************* 50896 typ host generation 0 ufrag sGkv network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"sGkv"}}
09:38:41.748 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:612708093 1 udp ********** ************* 49152 typ host generation 0 ufrag D+v6 network-id 2"}}
09:38:41.781 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1156323576 1 udp ********** *********** 49153 typ host generation 0 ufrag D+v6 network-id 1"}}
09:38:41.815 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:612708093 1 udp ********** ************* 49153 typ host generation 0 ufrag D+v6 network-id 2"}}
09:38:41.849 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1156323576 1 udp ********** *********** 49154 typ host generation 0 ufrag D+v6 network-id 1"}}
09:38:41.882 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:612708093 1 udp ********** ************* 49154 typ host generation 0 ufrag D+v6 network-id 2"}}
09:38:41.916 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:975436384 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag D+v6 network-id 1"}}
09:38:41.949 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1514851429 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag D+v6 network-id 2"}}
09:38:41.982 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:975436384 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag D+v6 network-id 1"}}
09:38:42.016 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1514851429 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag D+v6 network-id 2"}}
09:38:42.049 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:975436384 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag D+v6 network-id 1"}}
09:38:42.083 [37mStreamer Component -> 20[36m {"type":"iceCandidate","playerId":20,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1514851429 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag D+v6 network-id 2"}}
09:38:44.212 [37mStreamer Component ->[34m {"type":"ping","time":1753839524}
09:38:58.932 player 20 connection closed: 1001 - 
09:38:58.935 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"20"}
09:38:58.935 [37m[players] <-[32m {"type":"playerCount","count":0}
09:38:59.815 player 21 (::1) connected
09:38:59.815 [37m21 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:38:59.816 [37m[players] <-[32m {"type":"playerCount","count":1}
09:38:59.817 [37m21 ->[34m {"type":"listStreamers"}
09:38:59.817 [37m21 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:38:59.840 [37m21 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:38:59.840 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"21","dataChannel":true,"sfu":false,"sendOffer":true}
09:38:59.938 [37mStreamer Component -> 21[36m {"type":"offer","playerId":21,"sdp":"v=0\r\no=- 3062412353216695957 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jWB9\r\na=ice-pwd:s3TgWKjOlxb9nZjzVi39FAKK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:64:2C:56:AE:22:F6:0C:3D:E3:4F:BC:C3:F6:3D:BF:91:02:72:AB:BC:D6:CC:BE:28:A5:0F:61:38:4F:E1:22\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3790377939 2095009153\r\na=ssrc:3790377939 cname:t6osHdmypNzRXWUm\r\na=ssrc:3790377939 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2095009153 cname:t6osHdmypNzRXWUm\r\na=ssrc:2095009153 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jWB9\r\na=ice-pwd:s3TgWKjOlxb9nZjzVi39FAKK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:64:2C:56:AE:22:F6:0C:3D:E3:4F:BC:C3:F6:3D:BF:91:02:72:AB:BC:D6:CC:BE:28:A5:0F:61:38:4F:E1:22\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1402966362 cname:t6osHdmypNzRXWUm\r\na=ssrc:1402966362 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:jWB9\r\na=ice-pwd:s3TgWKjOlxb9nZjzVi39FAKK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:64:2C:56:AE:22:F6:0C:3D:E3:4F:BC:C3:F6:3D:BF:91:02:72:AB:BC:D6:CC:BE:28:A5:0F:61:38:4F:E1:22\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:59.971 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1966542716 1 udp ********** *********** 49152 typ host generation 0 ufrag jWB9 network-id 1"}}
09:38:59.983 [37m21 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5559648758812703567 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:d93T\r\na=ice-pwd:LzwcmFtCWNYKcULWDilzYTZP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C3:B1:D3:5A:0D:ED:1C:C2:D8:C9:2C:BF:4F:1D:85:67:73:35:83:C8:50:06:FF:36:9A:8E:DB:1F:B9:14:3C:D1\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:d93T\r\na=ice-pwd:LzwcmFtCWNYKcULWDilzYTZP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C3:B1:D3:5A:0D:ED:1C:C2:D8:C9:2C:BF:4F:1D:85:67:73:35:83:C8:50:06:FF:36:9A:8E:DB:1F:B9:14:3C:D1\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 59cbb055-5d46-4797-b342-e5f63384eb2a\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:987887868 cname:icMr1VyBsNUeY0g0\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:d93T\r\na=ice-pwd:LzwcmFtCWNYKcULWDilzYTZP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C3:B1:D3:5A:0D:ED:1C:C2:D8:C9:2C:BF:4F:1D:85:67:73:35:83:C8:50:06:FF:36:9A:8E:DB:1F:B9:14:3C:D1\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:59.984 [37m21 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1099013489 1 udp ********** *********** 55741 typ host generation 0 ufrag d93T network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"d93T"}}
09:38:59.985 [37m21 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:963860167 1 udp ********** ************* 55742 typ host generation 0 ufrag d93T network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"d93T"}}
09:39:00.005 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:87921349 1 udp ********** ************* 49152 typ host generation 0 ufrag jWB9 network-id 2"}}
09:39:00.039 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1966542716 1 udp ********** *********** 49153 typ host generation 0 ufrag jWB9 network-id 1"}}
09:39:00.072 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:87921349 1 udp ********** ************* 49153 typ host generation 0 ufrag jWB9 network-id 2"}}
09:39:00.106 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1966542716 1 udp ********** *********** 49154 typ host generation 0 ufrag jWB9 network-id 1"}}
09:39:00.140 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:87921349 1 udp ********** ************* 49154 typ host generation 0 ufrag jWB9 network-id 2"}}
09:39:00.174 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2342378472 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag jWB9 network-id 1"}}
09:39:00.207 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4220995153 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag jWB9 network-id 2"}}
09:39:00.241 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2342378472 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag jWB9 network-id 1"}}
09:39:00.275 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4220995153 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag jWB9 network-id 2"}}
09:39:00.308 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2342378472 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag jWB9 network-id 1"}}
09:39:00.342 [37mStreamer Component -> 21[36m {"type":"iceCandidate","playerId":21,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4220995153 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag jWB9 network-id 2"}}
09:39:44.248 [37mStreamer Component ->[34m {"type":"ping","time":1753839584}
09:40:11.766 player 21 connection closed: 1001 - 
09:40:11.770 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"21"}
09:40:11.771 [37m[players] <-[32m {"type":"playerCount","count":0}
09:40:14.049 player 22 (::1) connected
09:40:14.050 [37m22 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:40:14.050 [37m[players] <-[32m {"type":"playerCount","count":1}
09:40:14.050 [37m22 ->[34m {"type":"listStreamers"}
09:40:14.051 [37m22 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:40:14.075 [37m22 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:40:14.076 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"22","dataChannel":true,"sfu":false,"sendOffer":true}
09:40:14.128 [37mStreamer Component -> 22[36m {"type":"offer","playerId":22,"sdp":"v=0\r\no=- 6701215459594303622 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:DYp/\r\na=ice-pwd:PYK5m8+RUbnOOLItTlluIwCl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:9D:89:DA:84:D3:20:58:AE:4D:05:C5:2D:32:40:DD:5D:13:61:A6:12:CE:20:06:AF:4E:8D:26:42:B0:4E:BC\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3966446025 407550558\r\na=ssrc:3966446025 cname:FGBypBqq6OU4cX6K\r\na=ssrc:3966446025 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:407550558 cname:FGBypBqq6OU4cX6K\r\na=ssrc:407550558 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:DYp/\r\na=ice-pwd:PYK5m8+RUbnOOLItTlluIwCl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:9D:89:DA:84:D3:20:58:AE:4D:05:C5:2D:32:40:DD:5D:13:61:A6:12:CE:20:06:AF:4E:8D:26:42:B0:4E:BC\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4023019066 cname:FGBypBqq6OU4cX6K\r\na=ssrc:4023019066 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:DYp/\r\na=ice-pwd:PYK5m8+RUbnOOLItTlluIwCl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 A0:9D:89:DA:84:D3:20:58:AE:4D:05:C5:2D:32:40:DD:5D:13:61:A6:12:CE:20:06:AF:4E:8D:26:42:B0:4E:BC\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:40:14.160 [37m22 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5702967153208230425 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:HV/E\r\na=ice-pwd:uBWmNTK5TNTfhLzpC6OuX5Z+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 86:67:92:9B:5A:D2:B6:AE:A4:C4:A6:8A:16:3D:20:29:06:D5:D7:1A:3F:F9:87:38:18:A2:47:E8:85:02:4D:1F\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:HV/E\r\na=ice-pwd:uBWmNTK5TNTfhLzpC6OuX5Z+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 86:67:92:9B:5A:D2:B6:AE:A4:C4:A6:8A:16:3D:20:29:06:D5:D7:1A:3F:F9:87:38:18:A2:47:E8:85:02:4D:1F\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- c03db6fc-fe0c-4a11-aa2a-e8e7425c94cf\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2341997169 cname:fHHahUNVBoI05BAW\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:HV/E\r\na=ice-pwd:uBWmNTK5TNTfhLzpC6OuX5Z+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 86:67:92:9B:5A:D2:B6:AE:A4:C4:A6:8A:16:3D:20:29:06:D5:D7:1A:3F:F9:87:38:18:A2:47:E8:85:02:4D:1F\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:40:14.160 [37m22 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1513175878 1 udp ********** *********** 55601 typ host generation 0 ufrag HV/E network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"HV/E"}}
09:40:14.161 [37m22 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2939367182 1 udp ********** ************* 55602 typ host generation 0 ufrag HV/E network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"HV/E"}}
09:40:14.161 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2655687194 1 udp ********** *********** 49152 typ host generation 0 ufrag DYp/ network-id 1"}}
09:40:14.196 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3997175715 1 udp ********** ************* 49152 typ host generation 0 ufrag DYp/ network-id 2"}}
09:40:14.229 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2655687194 1 udp ********** *********** 49153 typ host generation 0 ufrag DYp/ network-id 1"}}
09:40:14.263 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3997175715 1 udp ********** ************* 49153 typ host generation 0 ufrag DYp/ network-id 2"}}
09:40:14.296 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2655687194 1 udp ********** *********** 49154 typ host generation 0 ufrag DYp/ network-id 1"}}
09:40:14.329 [37mStreamer Component -> 22[36m {"type":"iceCandidate","playerId":22,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3997175715 1 udp ********** ************* 49154 typ host generation 0 ufrag DYp/ network-id 2"}}
09:40:44.215 [37mStreamer Component ->[34m {"type":"ping","time":1753839644}
09:41:44.186 [37mStreamer Component ->[34m {"type":"ping","time":1753839704}
09:42:44.257 [37mStreamer Component ->[34m {"type":"ping","time":1753839764}
09:43:44.220 [37mStreamer Component ->[34m {"type":"ping","time":1753839824}
09:44:44.222 [37mStreamer Component ->[34m {"type":"ping","time":1753839884}
09:45:36.713 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
09:45:36.714 unsubscribing all players on Streamer Component
09:45:36.715 player 22 connection closed: 1005 - 
09:45:36.715 [37m[players] <-[32m {"type":"playerCount","count":0}
09:46:34.090 Streamer connected: ::1
09:46:34.091 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:46:34.092 [37m::1 <-[32m {"type":"identify"}
09:46:34.594 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:46:34.595 Registered new streamer: Streamer Component
09:47:34.658 [37mStreamer Component ->[34m {"type":"ping","time":1753840054}
09:47:38.117 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
09:47:38.117 unsubscribing all players on Streamer Component
