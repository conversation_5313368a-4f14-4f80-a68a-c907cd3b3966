{"name": "@epicgames-ps/pixelstreaming-sfu", "version": "1.0.1", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@epicgames-ps/pixelstreaming-sfu", "version": "1.0.1", "dependencies": {"mediasoup_prebuilt": "^3.8.4", "mediasoup-sdp-bridge": "file:mediasoup-sdp-bridge", "minimist": "^1.2.8", "run-script-os": "^1.1.6", "ws": "^7.5.10"}}, "mediasoup-sdp-bridge": {"name": "@epicgames-ps/mediasoup-sdp-bridge", "version": "3.6.5", "license": "ISC", "dependencies": {"mediasoup-client": "^3.6.41"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}}, "node_modules/@types/debug": {"version": "4.1.7", "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.7.tgz", "integrity": "sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/events": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@types/events/-/events-3.0.0.tgz", "integrity": "sha512-EaObqwIvayI5a8dCzhFrjKzVwKLxjoG9T6Ppd5CEo07LRKfQ8Yokw54r5+Wq7FaBQ+yXRvQAYPrHwya1/UFt9g=="}, "node_modules/@types/ms": {"version": "0.7.31", "resolved": "https://registry.npmjs.org/@types/ms/-/ms-0.7.31.tgz", "integrity": "sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA=="}, "node_modules/@types/node": {"version": "16.11.10", "resolved": "https://registry.npmjs.org/@types/node/-/node-16.11.10.tgz", "integrity": "sha512-3aRnHa1KlOEEhJ6+CvyHKK5vE9BcLGjtUpwvqYLRvYNQKMfabu3BwfJaA/SLW8dxe28LsNDjtHwePTuzn3gmOA=="}, "node_modules/awaitqueue": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/awaitqueue/-/awaitqueue-2.3.3.tgz", "integrity": "sha512-RbzQg6VtPUtyErm55iuQLTrBJ2uihy5BKBOEkyBwv67xm5Fn2o/j+Bz+a5BmfSoe2oZ5dcz9Z3fExS8pL+LLhw==", "engines": {"node": ">=8.0.0"}}, "node_modules/bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "node_modules/debug": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "engines": {"node": ">=0.8.x"}}, "node_modules/fake-mediastreamtrack": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/fake-mediastreamtrack/-/fake-mediastreamtrack-1.1.6.tgz", "integrity": "sha512-lcoO5oPsW57istAsnjvQxNjBEahi18OdUhWfmEewwfPfzNZnji5OXuodQM+VnUPi/1HnQRJ6gBUjbt1TNXrkjQ==", "dependencies": {"event-target-shim": "^5.0.1", "uuid": "^8.1.0"}}, "node_modules/h264-profile-level-id": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/h264-profile-level-id/-/h264-profile-level-id-1.0.1.tgz", "integrity": "sha512-D3Rln/jKNjKDW5ZTJTK3niSoOGE+pFqPvRHHVgQN3G7umcn/zWGPUo8Q8VpDj16x3hKz++zVviRNRmXu5cpN+Q==", "dependencies": {"debug": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/mediasoup_prebuilt": {"version": "3.8.4", "resolved": "https://registry.npmjs.org/mediasoup_prebuilt/-/mediasoup_prebuilt-3.8.4.tgz", "integrity": "sha512-IdPcuT3YTJXNFYAY4JuIy8sZ88qagKPg2dR8d4USR5csTvC+qOq9wAIywO+u2lxLjePHJH+Y8UBM3kKfyU6Uug==", "dependencies": {"@types/node": "^16.9.1", "awaitqueue": "^2.3.3", "debug": "^4.3.2", "h264-profile-level-id": "^1.0.1", "netstring": "^0.3.0", "random-number": "^0.0.9", "supports-color": "^9.0.2", "uuid": "^8.3.2"}}, "node_modules/mediasoup-client": {"version": "3.6.46", "resolved": "https://registry.npmjs.org/mediasoup-client/-/mediasoup-client-3.6.46.tgz", "integrity": "sha512-Dv8RxCa1cjSPrKWGf1mnypU5TiQCnrOIy4JpZwwjRQzEtCukCfV1zQabij6BigrtkI+l22ui3fl67Mmm4I0XCA==", "dependencies": {"@types/debug": "^4.1.7", "@types/events": "^3.0.0", "awaitqueue": "^2.3.3", "bowser": "^2.11.0", "debug": "^4.3.2", "events": "^3.3.0", "fake-mediastreamtrack": "^1.1.6", "h264-profile-level-id": "^1.0.1", "sdp-transform": "^2.14.1", "supports-color": "^9.1.0"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}}, "node_modules/mediasoup-sdp-bridge": {"resolved": "mediasoup-sdp-bridge", "link": true}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/netstring": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/netstring/-/netstring-0.3.0.tgz", "integrity": "sha1-ho3FsgxY0/cwVTHUk2jqqr0ZtxI=", "engines": {"node": ">=0.6"}}, "node_modules/random-number": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/random-number/-/random-number-0.0.9.tgz", "integrity": "sha512-ipG3kRCREi/YQpi2A5QGcvDz1KemohovWmH6qGfboVyyGdR2t/7zQz0vFxrfxpbHQgPPdtVlUDaks3aikD1Ljw=="}, "node_modules/run-script-os": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/run-script-os/-/run-script-os-1.1.6.tgz", "integrity": "sha512-ql6P2LzhBTTDfzKts+Qo4H94VUKpxKDFz6QxxwaUZN0mwvi7L3lpOI7BqPCq7lgDh3XLl0dpeXwfcVIitlrYrw==", "bin": {"run-os": "index.js", "run-script-os": "index.js"}}, "node_modules/sdp-transform": {"version": "2.14.1", "resolved": "https://registry.npmjs.org/sdp-transform/-/sdp-transform-2.14.1.tgz", "integrity": "sha512-RjZyX3nVwJyCuTo5tGPx+PZWkDMCg7oOLpSlhjDdZfwUoNqG1mM8nyj31IGHyaPWXhjbP7cdK3qZ2bmkJ1GzRw==", "bin": {"sdp-verify": "checker.js"}}, "node_modules/supports-color": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-9.1.0.tgz", "integrity": "sha512-lOCGOTmBSN54zKAoPWhHkjoqVQ0MqgzPE5iirtoSixhr0ZieR/6l7WZ32V53cvy9+1qghFnIk7k52p991lKd6g==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/ws": {"version": "7.5.10", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "integrity": "sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}, "dependencies": {"@types/debug": {"version": "4.1.7", "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.7.tgz", "integrity": "sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==", "requires": {"@types/ms": "*"}}, "@types/events": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@types/events/-/events-3.0.0.tgz", "integrity": "sha512-EaObqwIvayI5a8dCzhFrjKzVwKLxjoG9T6Ppd5CEo07LRKfQ8Yokw54r5+Wq7FaBQ+yXRvQAYPrHwya1/UFt9g=="}, "@types/ms": {"version": "0.7.31", "resolved": "https://registry.npmjs.org/@types/ms/-/ms-0.7.31.tgz", "integrity": "sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA=="}, "@types/node": {"version": "16.11.10", "resolved": "https://registry.npmjs.org/@types/node/-/node-16.11.10.tgz", "integrity": "sha512-3aRnHa1KlOEEhJ6+CvyHKK5vE9BcLGjtUpwvqYLRvYNQKMfabu3BwfJaA/SLW8dxe28LsNDjtHwePTuzn3gmOA=="}, "awaitqueue": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/awaitqueue/-/awaitqueue-2.3.3.tgz", "integrity": "sha512-RbzQg6VtPUtyErm55iuQLTrBJ2uihy5BKBOEkyBwv67xm5Fn2o/j+Bz+a5BmfSoe2oZ5dcz9Z3fExS8pL+LLhw=="}, "bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "debug": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "requires": {"ms": "2.1.2"}}, "event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ=="}, "events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="}, "fake-mediastreamtrack": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/fake-mediastreamtrack/-/fake-mediastreamtrack-1.1.6.tgz", "integrity": "sha512-lcoO5oPsW57istAsnjvQxNjBEahi18OdUhWfmEewwfPfzNZnji5OXuodQM+VnUPi/1HnQRJ6gBUjbt1TNXrkjQ==", "requires": {"event-target-shim": "^5.0.1", "uuid": "^8.1.0"}}, "h264-profile-level-id": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/h264-profile-level-id/-/h264-profile-level-id-1.0.1.tgz", "integrity": "sha512-D3Rln/jKNjKDW5ZTJTK3niSoOGE+pFqPvRHHVgQN3G7umcn/zWGPUo8Q8VpDj16x3hKz++zVviRNRmXu5cpN+Q==", "requires": {"debug": "^4.1.1"}}, "mediasoup_prebuilt": {"version": "3.8.4", "resolved": "https://registry.npmjs.org/mediasoup_prebuilt/-/mediasoup_prebuilt-3.8.4.tgz", "integrity": "sha512-IdPcuT3YTJXNFYAY4JuIy8sZ88qagKPg2dR8d4USR5csTvC+qOq9wAIywO+u2lxLjePHJH+Y8UBM3kKfyU6Uug==", "requires": {"@types/node": "^16.9.1", "awaitqueue": "^2.3.3", "debug": "^4.3.2", "h264-profile-level-id": "^1.0.1", "netstring": "^0.3.0", "random-number": "^0.0.9", "supports-color": "^9.0.2", "uuid": "^8.3.2"}}, "mediasoup-client": {"version": "3.6.46", "resolved": "https://registry.npmjs.org/mediasoup-client/-/mediasoup-client-3.6.46.tgz", "integrity": "sha512-Dv8RxCa1cjSPrKWGf1mnypU5TiQCnrOIy4JpZwwjRQzEtCukCfV1zQabij6BigrtkI+l22ui3fl67Mmm4I0XCA==", "requires": {"@types/debug": "^4.1.7", "@types/events": "^3.0.0", "awaitqueue": "^2.3.3", "bowser": "^2.11.0", "debug": "^4.3.2", "events": "^3.3.0", "fake-mediastreamtrack": "^1.1.6", "h264-profile-level-id": "^1.0.1", "sdp-transform": "^2.14.1", "supports-color": "^9.1.0"}}, "mediasoup-sdp-bridge": {"version": "file:mediasoup-sdp-bridge", "requires": {"mediasoup-client": "^3.6.41"}}, "minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "netstring": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/netstring/-/netstring-0.3.0.tgz", "integrity": "sha1-ho3FsgxY0/cwVTHUk2jqqr0ZtxI="}, "random-number": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/random-number/-/random-number-0.0.9.tgz", "integrity": "sha512-ipG3kRCREi/YQpi2A5QGcvDz1KemohovWmH6qGfboVyyGdR2t/7zQz0vFxrfxpbHQgPPdtVlUDaks3aikD1Ljw=="}, "run-script-os": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/run-script-os/-/run-script-os-1.1.6.tgz", "integrity": "sha512-ql6P2LzhBTTDfzKts+Qo4H94VUKpxKDFz6QxxwaUZN0mwvi7L3lpOI7BqPCq7lgDh3XLl0dpeXwfcVIitlrYrw=="}, "sdp-transform": {"version": "2.14.1", "resolved": "https://registry.npmjs.org/sdp-transform/-/sdp-transform-2.14.1.tgz", "integrity": "sha512-RjZyX3nVwJyCuTo5tGPx+PZWkDMCg7oOLpSlhjDdZfwUoNqG1mM8nyj31IGHyaPWXhjbP7cdK3qZ2bmkJ1GzRw=="}, "supports-color": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-9.1.0.tgz", "integrity": "sha512-lOCGOTmBSN54zKAoPWhHkjoqVQ0MqgzPE5iirtoSixhr0ZieR/6l7WZ32V53cvy9+1qghFnIk7k52p991lKd6g=="}, "uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="}, "ws": {"version": "7.5.10", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "integrity": "sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==", "requires": {}}}}