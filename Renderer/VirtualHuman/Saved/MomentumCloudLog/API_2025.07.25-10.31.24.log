2025.07.25-10.18.39	{"Background":{"backgroundMode":"Legacy"}}
2025.07.25-10.18.40	{"ScreenResolution":{"width":"1920.0","height":"1920.0"}}
2025.07.25-10.18.43	{"ResponseRequest":"all"}
2025.07.25-10.18.53	{"TextToSpeech":{"voiceConfig":{"voiceId":"MicrosoftZiraDesktop_en-US","voiceSpeed":"1.0"},"text":"我将为您播放蹲距式起跑技术教学视频"}}
2025.07.25-10.20.28	{"ResponseRequest":"all"}
2025.07.25-10.20.34	{"TextToSpeech":{"voiceConfig":{"voiceId":"MicrosoftHuihui_zh-CN"}}}
2025.07.25-10.20.56	{"ResponseRequest":"all"}
2025.07.25-10.21.01	{"Chatbot":{"model":"0"}}
2025.07.25-10.21.01	{"Chatbot":{"chatbotModel":"0"}}
2025.07.25-10.21.01	{"Chatbot":{"modelId":"0"}}
2025.07.25-10.21.01	{"Chatbot":{"chatbotModelId":"0"}}
2025.07.25-10.21.10	{"Chatbot":{"model":"2"}}
2025.07.25-10.21.10	{"Chatbot":{"chatbotModel":"2"}}
2025.07.25-10.21.10	{"Chatbot":{"modelId":"2"}}
2025.07.25-10.21.10	{"Chatbot":{"chatbotModelId":"2"}}
2025.07.25-10.21.15	{"ResponseRequest":"all"}
2025.07.25-10.22.55	{"ResponseRequest":"all"}
2025.07.25-10.24.34	{"TextToSpeech":{"synthesis":{"text":"你好","isSsml":0,"useStream":0,"secondText":""}}}
2025.07.25-10.27.03	{"ResponseRequest":"all"}
2025.07.25-10.27.46	{"ResponseRequest":"all"}
2025.07.25-10.29.53	{"ResponseRequest":"all"}
2025.07.25-10.30.10	{"ResponseRequest":"all"}
2025.07.25-10.30.22	{"ResponseRequest":"all"}
